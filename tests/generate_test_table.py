#!/usr/bin/env python3
"""
生成测试用的表格图像

用于透视变换对比测试的简单表格图像生成器
"""

import argparse
from PIL import Image, ImageDraw, ImageFont
import os

def generate_test_table(width=800, height=600, output_path="test_table.png"):
    """
    生成一个简单的测试表格图像
    
    Args:
        width: 图像宽度
        height: 图像高度  
        output_path: 输出文件路径
    """
    # 创建白色背景图像
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # 表格参数
    table_margin = 50
    table_width = width - 2 * table_margin
    table_height = height - 2 * table_margin
    table_left = table_margin
    table_top = table_margin
    
    rows = 6
    cols = 4
    cell_width = table_width // cols
    cell_height = table_height // rows
    
    # 绘制表格边框
    draw.rectangle([table_left, table_top, table_left + table_width, table_top + table_height], 
                  outline='black', width=2)
    
    # 绘制网格线
    # 垂直线
    for i in range(1, cols):
        x = table_left + i * cell_width
        draw.line([x, table_top, x, table_top + table_height], fill='black', width=1)
    
    # 水平线
    for i in range(1, rows):
        y = table_top + i * cell_height
        draw.line([table_left, y, table_left + table_width, y], fill='black', width=1)
    
    # 尝试加载字体
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)  # macOS
        except:
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 20)  # Linux
            except:
                font = ImageFont.load_default()  # 默认字体
    
    # 填充表格内容
    headers = ["产品", "数量", "单价", "总计"]
    data = [
        ["苹果", "10", "5.00", "50.00"],
        ["香蕉", "15", "3.00", "45.00"], 
        ["橙子", "8", "4.50", "36.00"],
        ["葡萄", "12", "8.00", "96.00"],
        ["总计", "", "", "227.00"]
    ]
    
    # 绘制表头（第一行，灰色背景）
    header_rect = [table_left, table_top, table_left + table_width, table_top + cell_height]
    draw.rectangle(header_rect, fill='lightgray', outline='black', width=1)
    
    for j, header in enumerate(headers):
        cell_x = table_left + j * cell_width
        cell_y = table_top
        
        # 计算文本位置（居中）
        bbox = draw.textbbox((0, 0), header, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        text_x = cell_x + (cell_width - text_width) // 2
        text_y = cell_y + (cell_height - text_height) // 2
        
        draw.text((text_x, text_y), header, fill='black', font=font)
    
    # 绘制数据行
    for i, row in enumerate(data):
        row_y = table_top + (i + 1) * cell_height
        
        # 最后一行（总计）使用浅黄色背景
        if i == len(data) - 1:
            total_rect = [table_left, row_y, table_left + table_width, row_y + cell_height]
            draw.rectangle(total_rect, fill='lightyellow', outline='black', width=1)
        
        for j, cell_text in enumerate(row):
            cell_x = table_left + j * cell_width
            
            # 计算文本位置（居中）
            bbox = draw.textbbox((0, 0), cell_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            text_x = cell_x + (cell_width - text_width) // 2
            text_y = row_y + (cell_height - text_height) // 2
            
            draw.text((text_x, text_y), cell_text, fill='black', font=font)
    
    # 添加标题
    title = "销售报表示例"
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
    except:
        title_font = font
    
    bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = bbox[2] - bbox[0]
    title_x = (width - title_width) // 2
    title_y = 20
    
    draw.text((title_x, title_y), title, fill='black', font=title_font)
    
    # 保存图像
    image.save(output_path)
    print(f"测试表格图像已生成: {output_path}")
    print(f"图像尺寸: {width}x{height}")
    print(f"表格尺寸: {table_width}x{table_height}")
    print(f"网格: {rows}行 x {cols}列")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成测试用表格图像")
    parser.add_argument("--width", "-w", type=int, default=800, help="图像宽度")
    parser.add_argument("--height", "-h", type=int, default=600, help="图像高度")
    parser.add_argument("--output", "-o", default="test_table.png", help="输出文件路径")
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成测试表格
    generate_test_table(args.width, args.height, args.output)

if __name__ == "__main__":
    main()

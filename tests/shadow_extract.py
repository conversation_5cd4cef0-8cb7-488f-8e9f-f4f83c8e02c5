import cv2
import numpy as np
import os
import json
import random
from PIL import Image


# 黑底阴影提取
def deshadow_dtsprompt(img, resize=False):
    """
    使用差分阴影跟踪提示（DTSPrompt）类似的方法估计图像的背景光照。
    """
    if resize:
        original_h, original_w = img.shape[:2]
        img_processed = cv2.resize(img, (1024, 1024))
    else:
        img_processed = img.copy()

    # 步骤 2: 分离颜色通道
    rgb_planes = cv2.split(img_processed)
    bg_planes_processed = []

    # 步骤 3: 处理每个颜色通道: 膨胀（亮区扩大）+中值滤波（大的核尺寸有助于估计背景）
    # 随机选择7-15之间的奇数作为核大小
    kernel_size = random.choice([7, 9, 11, 13, 15])
    for plane in rgb_planes:
        dilated_img = cv2.dilate(plane, np.ones((3, 3), np.uint8))
        bg_plane = cv2.medianBlur(dilated_img, kernel_size)
        bg_planes_processed.append(bg_plane)
    
    # 步骤 4: 合并处理后的颜色通道
    bg_img_processed = cv2.merge(bg_planes_processed)

    if resize:
        bg_img = cv2.resize(bg_img_processed, (original_w, original_h))
    else:
        bg_img = bg_img_processed

    return bg_img



# 黑底阴影合成
def blend_gt_and_shadow(gt_path, shadow_path):
    """
    参数：
        - gt_path: GT图像的路径
        - shadow_path: Shadow图像的路径
    """
    gt_img = cv2.imread(gt_path)
    shadow_img = cv2.imread(shadow_path)
    shadow_img_resized = cv2.resize(shadow_img, (gt_img.shape[1], gt_img.shape[0]))

    # Shadow图像融合计算
    gt_img = gt_img.astype(np.float64)
    shadow_img_resized = shadow_img_resized.astype(np.float64)
    blended = gt_img * (shadow_img_resized + 1) / 255
    blended = np.clip(blended, 0, 255).astype(np.uint8)

    # 转换为RGB格式并返回
    blended_rgb = cv2.cvtColor(blended, cv2.COLOR_BGR2RGB)

    return Image.fromarray(blended_rgb)


def get_table_dimensions(json_path):
    """
    从JSON标签文件中获取表格的行数和列数
    
    参数:
        json_path: JSON文件路径
    
    返回:
        tuple: (max_row, max_col) 表格的最大行数和列数，如果解析失败返回 (0, 0)
    """
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'cells' not in data or not data['cells']:
            return 0, 0
        
        max_row = 0
        max_col = 0
        
        for cell in data['cells']:
            if 'lloc' in cell:
                lloc = cell['lloc']
                if 'end_row' in lloc:
                    max_row = max(max_row, lloc['end_row'])
                if 'end_col' in lloc:
                    max_col = max(max_col, lloc['end_col'])
        
        return max_row + 1, max_col + 1  # 转换为实际行列数（索引+1）
    
    except Exception as e:
        print(f"解析JSON文件失败 {json_path}: {e}")
        return 0, 0


def process_tables(input_folder, output_folder, max_count=None):
    """
    批量处理表格图像，生成背景图
    
    参数:
        input_folder: 输入文件夹路径，包含多个子文件夹
        output_folder: 输出文件夹路径，所有背景图将保存在此根目录下
        max_count: 最大生成数量，达到此数量后停止处理，None表示不限制数量
    """
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)
    
    # 支持的图像格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    processed_count = 0
    skipped_count = 0
    
    # 遍历输入文件夹中的所有子文件夹
    for subfolder_name in os.listdir(input_folder):
        subfolder_path = os.path.join(input_folder, subfolder_name)
        
        if not os.path.isdir(subfolder_path):
            continue
        
        print(f"处理子文件夹: {subfolder_name}")
        
        # 获取子文件夹中的所有文件
        files = os.listdir(subfolder_path)
        
        # 找到图像文件和对应的JSON文件
        for file_name in files:
            file_path = os.path.join(subfolder_path, file_name)
            
            # 检查是否为图像文件
            name, ext = os.path.splitext(file_name)
            if ext.lower() not in image_extensions:
                continue
            
            # 查找对应的JSON文件
            json_file_name = name + '.json'
            json_path = os.path.join(subfolder_path, json_file_name)
            
            if not os.path.exists(json_path):
                print(f"  跳过 {file_name}: 找不到对应的JSON文件 {json_file_name}")
                skipped_count += 1
                continue
            
            try:
                # 获取表格尺寸
                rows, cols = get_table_dimensions(json_path)
                
                # 检查是否满足条件（行数或列数大于10）
                if rows <= 10 and cols <= 10:
                    print(f"  跳过 {file_name}: 表格尺寸 {rows}x{cols} 不满足条件（行或列需>10）")
                    skipped_count += 1
                    continue
                
                print(f"  处理 {file_name}: 表格尺寸 {rows}x{cols}")
                
                # 读取图像
                img = cv2.imread(file_path)
                if img is None:
                    print(f"  跳过 {file_name}: 无法读取图像文件")
                    skipped_count += 1
                    continue
                
                # 生成背景图
                bg_img = deshadow_dtsprompt(img, resize=False)
                
                # 生成输出文件名
                output_file_name = name + '_bg' + ext
                output_path = os.path.join(output_folder, output_file_name)
                
                # 保存背景图
                success = cv2.imwrite(output_path, bg_img)
                if success:
                    print(f"  成功生成背景图: {output_file_name}")
                    processed_count += 1
                    
                    # 检查是否达到数量上限
                    if max_count is not None and processed_count >= max_count:
                        print(f"\n已达到数量上限 {max_count}，停止处理")
                        print(f"成功处理: {processed_count} 张图像")
                        print(f"跳过: {skipped_count} 张图像")
                        return
                else:
                    print(f"  保存失败: {output_file_name}")
                    skipped_count += 1
                
            except Exception as e:
                print(f"  处理 {file_name} 时出错: {e}")
                skipped_count += 1
                continue
    
    print(f"\n处理完成！")
    print(f"成功处理: {processed_count} 张图像")
    print(f"跳过: {skipped_count} 张图像")


def main():
    # 示例用法
    input_folder = "/aipdf-mlp/xelawk/datasets/public/table_dataset_webui/data/release_split/train"
    output_folder = "/aipdf-mlp/jiacheng/code/tsr/TableRender/assets/transparent_ink_bgs"
    # 设置最大生成数量，例如生成100张背景图后停止
    process_tables(input_folder, output_folder, max_count=1000)
    
    # 原有的测试代码
    # img = cv2.imread("sample_data/bg1.jpg")
    # if img is not None:
    #     bg_img = deshadow_dtsprompt(img, resize=True)
    #     cv2.imwrite("sample_data/bg1_deshadow.png", bg_img)
    #     print("测试完成: sample_data/bg1_deshadow.png")
    # else:
    #     print("测试图像不存在: sample_data/bg1.jpg")


if __name__ == "__main__":
    main()

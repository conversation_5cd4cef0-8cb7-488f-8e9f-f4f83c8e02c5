#!/usr/bin/env python3
"""
JSON/JSONL表格数据转CSV转换器

将nl2sql_test_tables.json和wikisql_test_tables.jsonl格式的表格数据
转换为CSV格式，按行数分组并横向拼接。

用法:
    python json_to_csv_converter.py <input_path> <output_dir> [--batch-size 20]
"""

import json
import csv
import argparse
import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Union
from collections import defaultdict


class TableData:
    """表格数据类"""
    
    def __init__(self, header: List[str], rows: List[List[Any]]):
        self.header = header
        self.rows = rows
        # 修复：header只占1行，不是len(header)行
        self.row_count = (1 if header else 0) + len(rows)
        self.col_count = len(header) if header else (len(rows[0]) if rows else 0)
    
    def to_csv_rows(self) -> List[List[str]]:
        """转换为CSV行格式"""
        csv_rows = []
        
        # 添加header行（如果存在）
        if self.header:
            csv_rows.append([str(cell) if cell is not None else "" for cell in self.header])
        
        # 添加数据行
        for row in self.rows:
            csv_rows.append([str(cell) if cell is not None else "" for cell in row])
        
        return csv_rows


class JsonToCsvConverter:
    """JSON到CSV转换器"""
    
    def __init__(self, batch_size: int = 20):
        self.batch_size = batch_size
        self.groups = defaultdict(list)  # {row_count: [TableData, ...]}
        self.stats = {
            'total_files': 0,
            'total_tables': 0,
            'skipped_tables': 0,
            'generated_csvs': 0
        }
    
    def parse_json_file(self, file_path: Path) -> List[TableData]:
        """解析JSON文件，返回表格数据列表"""
        tables = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 按行读取，尝试JSONL格式
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        data = json.loads(line)
                        table = self._extract_table_data(data, f"{file_path}:{line_num}")
                        if table:
                            tables.append(table)
                    except json.JSONDecodeError as e:
                        print(f"警告: {file_path}:{line_num} JSON解析失败: {e}")
                        self.stats['skipped_tables'] += 1
                        continue

        except Exception as e:
            print(f"错误: 无法读取文件 {file_path}: {e}")

        return tables
    
    def _extract_table_data(self, data: Dict[str, Any], source: str) -> Union[TableData, None]:
        """从JSON数据中提取表格数据"""
        try:
            header = data.get('header', [])
            rows = data.get('rows', [])

            # 确保header和rows都是列表
            if not isinstance(header, list):
                header = []
            if not isinstance(rows, list):
                rows = []

            # 创建表格数据对象（清理工作在add_table中进行）
            table = TableData(header, rows)

            # 基本检查：确保不是完全空的表格
            if not header and not rows:
                print(f"跳过: {source} - 完全空的表格")
                self.stats['skipped_tables'] += 1
                return None

            return table

        except Exception as e:
            print(f"警告: {source} 数据提取失败: {e}")
            self.stats['skipped_tables'] += 1
            return None

    def _clean_empty_rows_and_columns(self, header: List[str], rows: List[List[Any]], source: str) -> tuple:
        """
        清理空行和空列

        Args:
            header: 表头列表
            rows: 数据行列表
            source: 数据源标识（用于日志）

        Returns:
            (清理后的header, 清理后的rows)
        """
        if not header and not rows:
            return header, rows

        original_header_count = len(header)
        original_row_count = len(rows)

        # 第一步：移除完全空白的数据行
        cleaned_rows = []
        for row in rows:
            # 检查行是否完全为空（所有单元格都是None、空字符串或只有空白字符）
            if self._is_row_empty(row):
                continue
            cleaned_rows.append(row)

        # 第二步：确定最大列数，统一行长度
        max_cols = len(header) if header else 0
        for row in cleaned_rows:
            max_cols = max(max_cols, len(row))

        # 统一header长度
        if header:
            while len(header) < max_cols:
                header.append("")
        else:
            header = [""] * max_cols

        # 统一所有行的长度
        for row in cleaned_rows:
            while len(row) < max_cols:
                row.append("")

        # 第三步：识别并移除完全空白的列
        if max_cols > 0:
            non_empty_col_indices = []
            for col_idx in range(max_cols):
                # 检查这一列是否完全为空
                col_empty = True

                # 检查header中的这一列
                if header and col_idx < len(header) and not self._is_cell_empty(header[col_idx]):
                    col_empty = False

                # 检查所有数据行中的这一列
                if col_empty:
                    for row in cleaned_rows:
                        if col_idx < len(row) and not self._is_cell_empty(row[col_idx]):
                            col_empty = False
                            break

                if not col_empty:
                    non_empty_col_indices.append(col_idx)

            # 重建header和rows，只保留非空列
            if non_empty_col_indices:
                new_header = [header[i] for i in non_empty_col_indices] if header else []
                new_rows = []
                for row in cleaned_rows:
                    new_row = [row[i] if i < len(row) else "" for i in non_empty_col_indices]
                    new_rows.append(new_row)

                header = new_header
                cleaned_rows = new_rows
            else:
                # 所有列都是空的，返回空表格
                header = []
                cleaned_rows = []

        # 记录清理统计
        removed_rows = original_row_count - len(cleaned_rows)
        removed_cols = original_header_count - len(header)

        if removed_rows > 0 or removed_cols > 0:
            print(f"清理: {source} - 移除 {removed_rows} 个空行, {removed_cols} 个空列")

        return header, cleaned_rows

    def _is_row_empty(self, row: List[Any]) -> bool:
        """检查行是否为空"""
        if not row:
            return True
        return all(self._is_cell_empty(cell) for cell in row)

    def _is_cell_empty(self, cell: Any) -> bool:
        """检查单元格是否为空"""
        if cell is None:
            return True
        if isinstance(cell, str) and cell.strip() == "":
            return True
        if isinstance(cell, (int, float)) and cell == 0:
            return False  # 数字0不算空
        return False

    def add_table(self, table: TableData):
        """添加表格到对应的行数组（基于清理后的行数）"""
        # 先清理表格，然后按清理后的行数分组
        cleaned_header, cleaned_rows = self._clean_empty_rows_and_columns(
            table.header.copy(),
            [row.copy() for row in table.rows],
            f"add_table_{self.stats['total_tables']}"
        )

        # 创建清理后的表格对象
        cleaned_table = TableData(cleaned_header, cleaned_rows)

        # 检查清理后的最小行数限制
        if cleaned_table.row_count < 2:
            print(f"跳过: 表格清理后行数不足2行 (清理后: {cleaned_table.row_count})")
            self.stats['skipped_tables'] += 1
            return

        # 按清理后的行数分组
        self.groups[cleaned_table.row_count].append(cleaned_table)
        self.stats['total_tables'] += 1
    
    def process_files(self, input_path: Path):
        """处理输入文件或目录"""
        if input_path.is_file():
            # 处理单个文件
            if input_path.suffix in ['.json', '.jsonl']:
                self.stats['total_files'] += 1
                tables = self.parse_json_file(input_path)
                for table in tables:
                    self.add_table(table)
            else:
                print(f"警告: 跳过非JSON文件 {input_path}")
        
        elif input_path.is_dir():
            # 处理目录中的所有JSON文件
            for file_path in input_path.glob('*.json'):
                self.stats['total_files'] += 1
                tables = self.parse_json_file(file_path)
                for table in tables:
                    self.add_table(table)
            
            for file_path in input_path.glob('*.jsonl'):
                self.stats['total_files'] += 1
                tables = self.parse_json_file(file_path)
                for table in tables:
                    self.add_table(table)
        
        else:
            raise FileNotFoundError(f"输入路径不存在: {input_path}")
    
    def merge_tables(self, tables: List[TableData]) -> List[List[str]]:
        """横向拼接表格（表格已经是清理后的）"""
        if not tables:
            return []

        # 确保所有表格行数相同（应该已经相同，因为是按行数分组的）
        row_count = tables[0].row_count
        for i, table in enumerate(tables):
            if table.row_count != row_count:
                raise ValueError(f"表格行数不一致: 子表{i}为{table.row_count}行，期望{row_count}行")

        # 初始化结果矩阵
        merged_rows = [[] for _ in range(row_count)]

        # 横向拼接每个表格
        for table in tables:
            csv_rows = table.to_csv_rows()
            for i, row in enumerate(csv_rows):
                merged_rows[i].extend(row)

        return merged_rows

    def save_csv_batch(self, tables: List[TableData], output_path: Path):
        """保存一批表格为CSV文件"""
        try:
            merged_rows = self.merge_tables(tables)
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerows(merged_rows)
            
            self.stats['generated_csvs'] += 1
            print(f"生成: {output_path} ({len(tables)}个表格, {len(merged_rows)}行)")
            
        except Exception as e:
            print(f"错误: 保存CSV失败 {output_path}: {e}")
    
    def generate_csvs(self, output_dir: Path):
        """生成所有CSV文件"""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for row_count, tables in self.groups.items():
            batch_num = 1
            
            # 按批次处理表格
            for i in range(0, len(tables), self.batch_size):
                batch_tables = tables[i:i + self.batch_size]
                filename = f"{row_count:04d}rows_batch_{batch_num:03d}.csv"
                output_path = output_dir / filename
                
                self.save_csv_batch(batch_tables, output_path)
                batch_num += 1
    
    def print_stats(self):
        """打印处理统计信息"""
        print("\n=== 处理统计 ===")
        print(f"处理文件数: {self.stats['total_files']}")
        print(f"总表格数: {self.stats['total_tables']}")
        print(f"跳过表格数: {self.stats['skipped_tables']}")
        print(f"生成CSV数: {self.stats['generated_csvs']}")
        
        print("\n=== 按行数分布 ===")
        for row_count in sorted(self.groups.keys()):
            table_count = len(self.groups[row_count])
            batch_count = (table_count + self.batch_size - 1) // self.batch_size
            print(f"{row_count:2d}行: {table_count:3d}个表格 -> {batch_count:2d}个CSV文件")



def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='JSON/JSONL表格数据转CSV转换器')
    parser.add_argument('input_path', nargs='?', default='./assets/corpus/origin/wikisql_train_tables.jsonl', help='输入文件或目录路径')
    parser.add_argument('output_dir', nargs='?', default='./assets/corpus/wikisql_train/', help='输出目录路径')
    parser.add_argument('--batch-size', type=int, default=20, help='每批次表格数量 (默认: 20)')

    args = parser.parse_args()

    # 验证输入路径
    input_path = Path(args.input_path)
    if not input_path.exists():
        print(f"错误: 输入路径不存在: {input_path}")
        sys.exit(1)

    # 创建转换器
    converter = JsonToCsvConverter(batch_size=args.batch_size)

    try:
        # 处理输入文件
        print(f"开始处理: {input_path}")
        converter.process_files(input_path)

        # 生成CSV文件
        output_dir = Path(args.output_dir)
        print(f"输出目录: {output_dir}")
        converter.generate_csvs(output_dir)

        # 打印统计信息
        converter.print_stats()

    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

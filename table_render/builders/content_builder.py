"""
内容生成器

负责为表格结构填充内容。
"""

import csv
import logging
import numpy as np
from pathlib import Path
from faker import Faker

from ..models import TableModel
from ..config import ContentConfig, ResolvedContentParams


class ContentBuilder:
    """
    内容生成器类
    
    为已有的表格结构填充简单的占位内容。
    """
    
    def __init__(self, seed: int):
        """
        初始化内容生成器

        Args:
            seed: 随机种子，用于确保可复现性
        """
        self.random_state = np.random.RandomState(seed)
        self.faker = Faker()
        self.faker.seed_instance(seed)
        self.logger = logging.getLogger(__name__)
        
    def build(self, table_model: TableModel, config: ResolvedContentParams) -> TableModel:
        """
        为表格模型填充内容

        Args:
            table_model: 由StructureBuilder生成的、仅有结构的表格模型
            config: 解析后的内容参数

        Returns:
            填充了内容的表格模型
        """
        self.logger.debug(f"开始填充表格内容，使用源类型: {config.source_type}")

        # 根据配置的 source_type 分派到不同的处理函数
        if config.source_type == 'programmatic':
            self._fill_programmatically(table_model, config)
        elif config.source_type == 'csv':
            if config.csv_file_path is None:
                raise ValueError("CSV source is selected but 'csv_file_path' is missing.")
            self._fill_from_csv(table_model, config)
        else:
            raise NotImplementedError(f"Content source type '{config.source_type}' is not supported.")

        self.logger.debug("表格内容填充完成")
        return table_model

    def _fill_programmatically(self, table_model: TableModel, config: ResolvedContentParams):
        """
        使用程序化方法填充表格内容

        Args:
            table_model: 表格模型
            config: 解析后的内容参数
        """
        # 遍历所有单元格并填充内容
        for row in table_model.rows:
            # V5.1修复：检查行是否有单元格，避免空行导致的问题
            if not row.cells:
                self.logger.warning(f"[CONTENT_BUILDER] 发现空行，跳过内容填充: row_index={row.row_index}")
                continue
            for cell in row.cells:
                if cell.is_header:
                    # 为表头单元格生成列标题
                    cell.content = f"Column {cell.col_index + 1}"
                else:
                    # 为数据单元格生成格式化内容
                    cell.content = self._get_random_programmatic_content(config.programmatic_types)

    def _get_random_programmatic_content(self, types: list) -> str:
        """
        生成随机的程序化内容

        Args:
            types: 支持的内容类型列表

        Returns:
            生成的内容字符串
        """
        if not types:
            return f"Data {self.random_state.randint(1, 1000)}"

        content_type = self.random_state.choice(types)

        if content_type == 'date':
            return self.faker.date()
        elif content_type == 'currency':
            amount = self.random_state.uniform(10.0, 10000.0)
            return f"${amount:.2f}"
        elif content_type == 'percentage':
            percent = self.random_state.uniform(0.0, 100.0)
            return f"{percent:.1f}%"
        elif content_type == 'text':
            # V4.0新增：生成长文本用于测试内容溢出
            return self.faker.sentence(nb_words=self.random_state.randint(5, 15))
        else:
            # 默认生成简单的数字内容
            return f"Value {self.random_state.randint(1, 1000)}"

    def _fill_from_csv(self, table_model: TableModel, config: ResolvedContentParams):
        """
        从CSV文件填充表格内容

        Args:
            table_model: 表格模型
            config: 解析后的内容参数
        """
        try:
            # 加载CSV数据
            csv_data = self._load_csv_data(config)

            if not csv_data:
                self.logger.warning("CSV数据为空，跳过填充")
                return

            # V5.0新增：根据采样模式选择填充方法
            sampling_mode = getattr(config, 'sampling_mode', 'positional')

            self.logger.info(f"开始CSV内容填充，模式: {sampling_mode}, 文件: {config.csv_file_path}")

            if sampling_mode == 'random':
                self._fill_with_random_sampling(table_model, csv_data, config)
            else:
                # 保持原有的位置对应模式
                self._fill_with_positional_mapping(table_model, csv_data, config)

            self.logger.debug("CSV内容填充完成")

        except (FileNotFoundError, ValueError) as e:
            self.logger.error(f"CSV内容填充失败: {e}")
            # 对于关键错误，重新抛出
            raise
        except Exception as e:
            self.logger.error(f"CSV内容填充时发生未预期错误: {e}")
            raise

    def _load_csv_data(self, config: ResolvedContentParams) -> list:
        """
        加载CSV文件数据

        Args:
            config: 解析后的内容参数

        Returns:
            二维列表形式的CSV数据

        Raises:
            FileNotFoundError: 如果CSV文件不存在
            ValueError: 如果CSV文件格式不符合要求
        """
        csv_path = Path(config.csv_file_path)
        if not csv_path.exists():
            raise FileNotFoundError(f"CSV文件未找到: {config.csv_file_path}")

        csv_data = []
        try:
            with open(csv_path, 'r', encoding=config.csv_encoding) as f:
                reader = csv.reader(f)
                for row_num, row in enumerate(reader, 1):
                    csv_data.append(row)

        except UnicodeDecodeError as e:
            raise ValueError(f"CSV文件编码错误 {config.csv_file_path}: {e}")
        except Exception as e:
            raise ValueError(f"读取CSV文件失败 {config.csv_file_path}: {e}")

        # V5.0新增：验证CSV数据格式
        self._validate_csv_data(csv_data, config.csv_file_path)

        self.logger.debug(f"成功加载CSV文件: {config.csv_file_path}, {len(csv_data)}行")
        return csv_data

    def _validate_csv_data(self, csv_data: list, file_path: str):
        """
        验证CSV数据格式

        Args:
            csv_data: CSV数据
            file_path: 文件路径（用于错误信息）

        Raises:
            ValueError: 如果数据格式不符合要求
        """
        if not csv_data:
            raise ValueError(f"CSV文件为空: {file_path}")

        if len(csv_data) < 2:
            raise ValueError(f"CSV文件行数不足({len(csv_data)}行)，需要至少2行: {file_path}")

        # 检查第一行（表头）
        header_row = csv_data[0]
        if not header_row or len(header_row) < 2:
            raise ValueError(f"CSV表头列数不足({len(header_row) if header_row else 0}列)，需要至少2列: {file_path}")

        # 检查是否有有效的数据行
        valid_data_rows = 0
        for i, row in enumerate(csv_data[1:], 2):
            if row and len(row) >= 2:  # 至少有2列且不为空
                valid_data_rows += 1

        if valid_data_rows == 0:
            raise ValueError(f"CSV文件没有有效的数据行（需要至少2列且非空）: {file_path}")

        self.logger.debug(f"CSV数据验证通过: {file_path}, 表头{len(header_row)}列, {valid_data_rows}个有效数据行")

    def _fill_with_positional_mapping(self, table_model: TableModel, csv_data: list, config: ResolvedContentParams):
        """
        使用位置对应方式填充表格内容（原有逻辑）

        Args:
            table_model: 表格模型
            csv_data: CSV数据
            config: 解析后的内容参数
        """
        num_csv_rows = len(csv_data)
        num_csv_cols = len(csv_data[0]) if num_csv_rows > 0 else 0

        self.logger.debug(f"使用位置对应模式填充，CSV数据: {num_csv_rows}x{num_csv_cols}")

        # 遍历 TableModel 填充内容
        for row in table_model.rows:
            # V5.1修复：检查行是否有单元格，避免空行导致的问题
            if not row.cells:
                self.logger.warning(f"[CONTENT_BUILDER] 发现空行，跳过CSV填充: row_index={row.row_index}")
                continue
            for cell in row.cells:
                r_idx = cell.row_index
                c_idx = cell.col_index

                # 检查CSV数据源是否越界
                if r_idx < num_csv_rows and c_idx < num_csv_cols:
                    cell.content = csv_data[r_idx][c_idx]
                else:
                    # 处理行列数不匹配的情况
                    strategy = config.csv_mismatch_strategy
                    if strategy == 'fill_empty':
                        cell.content = ""
                    # 如果是 'truncate'，则不执行任何操作，内容将保持默认的空字符串

    def _fill_with_random_sampling(self, table_model: TableModel, csv_data: list, config: ResolvedContentParams):
        """
        使用随机采样方式填充表格内容（V5.0重构：行列对应采样）

        Args:
            table_model: 表格模型
            csv_data: CSV数据
            config: 解析后的内容参数
        """
        if len(csv_data) < 2:
            self.logger.warning(f"CSV数据行数不足({len(csv_data)}行)，需要至少2行，跳过随机采样")
            return

        # 分析CSV数据结构
        csv_header = csv_data[0]
        csv_data_rows = csv_data[1:]
        csv_col_count = len(csv_header)
        csv_data_row_count = len(csv_data_rows)

        if csv_col_count == 0:
            self.logger.warning("CSV表头为空，跳过随机采样")
            return

        self.logger.debug(f"CSV数据结构: {csv_col_count}列, {csv_data_row_count}个数据行")

        # 分析表格需求
        # V5.1修复：安全地获取列数，考虑空行的情况
        table_col_count = 0
        for row in table_model.rows:
            if row.cells:  # 只考虑非空行
                table_col_count = len(row.cells)
                break

        table_header_row_count = sum(1 for row in table_model.rows
                                   if row.cells and any(cell.is_header for cell in row.cells))
        table_data_row_count = len(table_model.rows) - table_header_row_count

        self.logger.debug(f"表格需求: {table_col_count}列, {table_header_row_count}个表头行, {table_data_row_count}个数据行")

        # 随机选择列索引（优先不放回采样）
        selected_col_indices = self._smart_sample(csv_col_count, table_col_count)

        # 随机选择数据行索引（优先不放回采样）
        selected_row_indices = self._smart_sample(csv_data_row_count, table_data_row_count)

        self.logger.debug(f"选择的列索引: {selected_col_indices}")
        self.logger.debug(f"选择的行索引: {selected_row_indices}")

        # V5.0新增：应用空白控制
        blank_mask = self._generate_blank_mask(
            table_model, config,
            table_col_count, table_data_row_count
        )

        # 保存CSV采样信息到表格模型（V5.0新增）
        self._save_csv_sampling_metadata(
            table_model, config,
            selected_col_indices, selected_row_indices,
            csv_col_count, csv_data_row_count
        )

        # 按行列对应关系填充表格
        self._fill_table_with_selected_indices(
            table_model, csv_data,
            selected_col_indices, selected_row_indices,
            blank_mask
        )

    def _fill_table_with_selected_indices(
        self,
        table_model: TableModel,
        csv_data: list,
        selected_col_indices: list,
        selected_row_indices: list,
        blank_mask: set = None
    ):
        """
        根据预选的行列索引填充表格

        Args:
            table_model: 表格模型
            csv_data: CSV数据
            selected_col_indices: 选中的列索引列表
            selected_row_indices: 选中的数据行索引列表
            blank_mask: 空白单元格掩码，格式为 {(row_idx, col_idx), ...}
        """
        if blank_mask is None:
            blank_mask = set()
        csv_header = csv_data[0]
        csv_data_rows = csv_data[1:]

        data_row_counter = 0  # 数据行计数器

        for table_row in table_model.rows:
            # V5.1修复：检查行是否有单元格，避免空行导致的问题
            if not table_row.cells:
                self.logger.warning(f"[CONTENT_BUILDER] 发现空行，跳过随机采样填充: row_index={table_row.row_index}")
                continue
            for col_idx, cell in enumerate(table_row.cells):
                if col_idx >= len(selected_col_indices):
                    # 防止索引越界
                    cell.content = ""
                    continue

                csv_col_idx = selected_col_indices[col_idx]

                if cell.is_header:
                    # 表头单元格：从CSV表头行获取（表头永远不为空白）
                    if csv_col_idx < len(csv_header):
                        cell.content = str(csv_header[csv_col_idx]) if csv_header[csv_col_idx] is not None else ""
                    else:
                        cell.content = ""
                else:
                    # 数据单元格：检查是否应该为空白
                    if (data_row_counter, col_idx) in blank_mask:
                        cell.content = ""
                    elif data_row_counter < len(selected_row_indices):
                        csv_row_idx = selected_row_indices[data_row_counter]
                        # V5.0修复：正确处理CSV行列索引异常
                        if (csv_row_idx < len(csv_data_rows) and
                            csv_col_idx < len(csv_header) and
                            csv_col_idx < len(csv_data_rows[csv_row_idx])):
                            cell.content = str(csv_data_rows[csv_row_idx][csv_col_idx]) if csv_data_rows[csv_row_idx][csv_col_idx] is not None else ""
                        else:
                            # 索引超出范围，设为空白
                            cell.content = ""
                    else:
                        cell.content = ""

            # 如果这一行包含数据单元格，增加数据行计数器
            if any(not cell.is_header for cell in table_row.cells):
                data_row_counter += 1

    def _smart_sample(self, available_count: int, needed_count: int) -> list:
        """
        智能采样：优先不放回采样，不够时再重复采样

        Args:
            available_count: 可用元素数量
            needed_count: 需要的元素数量

        Returns:
            选中的索引列表
        """
        if needed_count <= 0:
            return []

        if available_count <= 0:
            return [0] * needed_count  # 边界情况处理

        if needed_count <= available_count:
            # 不放回采样：使用numpy.random.choice实现
            return self.random_state.choice(available_count, size=needed_count, replace=False).tolist()
        else:
            # 不够用：先全选，然后重新随机采样补充剩余的
            all_indices = list(range(available_count))
            additional_needed = needed_count - available_count
            additional_indices = [self.random_state.randint(0, available_count) for _ in range(additional_needed)]
            return all_indices + additional_indices

    def _save_csv_sampling_metadata(
        self,
        table_model,
        config,
        selected_col_indices: list,
        selected_row_indices: list,
        csv_total_columns: int,
        csv_total_rows: int
    ):
        """
        保存CSV采样元数据到表格模型

        Args:
            table_model: 表格模型
            config: 解析后的内容参数
            selected_col_indices: 选中的列索引
            selected_row_indices: 选中的行索引
            csv_total_columns: CSV总列数
            csv_total_rows: CSV总数据行数
        """
        from ..models import CSVSamplingMetadata

        # 创建采样元数据
        sampling_metadata = CSVSamplingMetadata(
            source_file=config.csv_file_path or "",
            selected_columns=selected_col_indices.copy(),
            selected_rows=selected_row_indices.copy(),
            csv_total_columns=csv_total_columns,
            csv_total_rows=csv_total_rows,
            sampling_mode=getattr(config, 'sampling_mode', 'random')
        )

        # 保存到表格模型
        table_model.csv_sampling_metadata = sampling_metadata

        self.logger.debug(f"保存CSV采样元数据: 列{selected_col_indices}, 行{selected_row_indices}")

    def _generate_blank_mask(
        self,
        table_model,
        config,
        table_col_count: int,
        table_data_row_count: int
    ) -> set:
        """
        生成空白掩码：决定哪些单元格应该为空白

        Args:
            table_model: 表格模型
            config: 解析后的内容参数
            table_col_count: 表格列数
            table_data_row_count: 表格数据行数

        Returns:
            空白单元格位置的集合 {(row_idx, col_idx), ...}
        """
        blank_mask = set()

        # 获取空白控制配置
        blank_control = getattr(config, 'blank_control', None)
        if not blank_control:
            return blank_mask

        trigger_prob = blank_control.get('trigger_probability', 0.0)
        cell_blank_prob = blank_control.get('cell_blank_probability', 0.3)

        if trigger_prob <= 0:
            return blank_mask

        # 第一级：决定哪些行被标记为可空白
        blank_rows = set()
        for row_idx in range(table_data_row_count):
            if self.random_state.random() < trigger_prob:
                blank_rows.add(row_idx)

        # 第一级：决定哪些列被标记为可空白
        blank_cols = set()
        for col_idx in range(table_col_count):
            if self.random_state.random() < trigger_prob:
                blank_cols.add(col_idx)

        # 第二级：在标记的行中，每个单元格按概率变空
        for row_idx in blank_rows:
            for col_idx in range(table_col_count):
                if self.random_state.random() < cell_blank_prob:
                    blank_mask.add((row_idx, col_idx))

        # 第二级：在标记的列中，每个单元格按概率变空
        for col_idx in blank_cols:
            for row_idx in range(table_data_row_count):
                if self.random_state.random() < cell_blank_prob:
                    blank_mask.add((row_idx, col_idx))

        self.logger.debug(f"生成空白掩码: {len(blank_mask)}个空白单元格")
        return blank_mask

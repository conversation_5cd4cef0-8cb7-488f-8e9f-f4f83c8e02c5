"""
样式生成器

负责根据配置生成CSS样式字符串，控制表格的视觉外观。
V3.2版本增强：支持样式继承、智能字体选择、颜色对比度保证、边框系统增强等新特性。
"""

import logging
import numpy as np
import os
from pathlib import Path
from typing import Union, List, Any, Dict

from ..config import StyleConfig, FontConfig, RangeConfig, ResolvedStyleParams, ResolvedSizingParams
from ..utils.font_utils import FontManager
from ..utils.color_utils import ColorManager
from ..utils.border_color_utils import BorderColorManager
from ..utils.color_diff_utils import ColorDifferenceDetector
from ..models import BorderDecisions


class StyleBuilder:
    """
    样式生成器类
    
    根据配置生成CSS样式，包括字体、颜色、边框、对齐方式等。
    """
    
    def __init__(self, seed: int):
        """
        初始化样式生成器

        Args:
            seed: 随机种子，用于确保可复现性
        """
        self.random_state = np.random.RandomState(seed)
        self.logger = logging.getLogger(__name__)

        # V3.2新增：初始化工具管理器
        self.font_manager = FontManager()
        self.color_manager = ColorManager()

        # V4.5新增：边框颜色管理器
        self.border_color_manager = BorderColorManager(self.color_manager)

        # V4.5新增：颜色差异检测器
        self.color_diff_detector = ColorDifferenceDetector(self.color_manager)
        
    def build(self, config: ResolvedStyleParams, table_model=None, transparency_config=None) -> str:
        """
        构建CSS样式字符串

        Args:
            config: 解析后的样式参数
            table_model: 表格模型（包含边框决策信息）
            transparency_config: V4.3新增：透明度配置参数

        Returns:
            完整的CSS样式字符串
        """
        css_parts = []

        # 1. V3.2增强：生成 @font-face 规则（支持多字体目录）
        header_font_rules = self._generate_font_face_rules(config.header.font)
        body_font_rules = self._generate_font_face_rules(config.body.font)
        if header_font_rules:
            css_parts.append(header_font_rules)
        if body_font_rules and body_font_rules != header_font_rules:
            css_parts.append(body_font_rules)

        # 2. 生成全局和表格基础样式
        global_rules = self._generate_global_rules()
        css_parts.append(global_rules)

        # 3. V3.1新逻辑：生成独立的表头和主体样式
        header_rules = self._generate_header_rules(config.header, transparency_config)
        css_parts.append(header_rules)

        # 4. 生成表体样式或斑马条纹样式（互斥）
        if config.zebra_stripes and config.zebra_colors:
            zebra_rules = self._generate_zebra_stripes_rules(config.body, config.zebra_colors, transparency_config)
            css_parts.append(zebra_rules)
        else:
            body_rules = self._generate_body_rules(config.body, transparency_config)
            css_parts.append(body_rules)

        # 5. 生成分层样式规则
        if config.hierarchical:
            hierarchical_rules = self._generate_hierarchical_rules(config.hierarchical)
            if hierarchical_rules:
                css_parts.append(hierarchical_rules)

        # 6. 生成行列尺寸样式
        sizing_rules = self._generate_sizing_rules(config.sizing)
        if sizing_rules:
            css_parts.append(sizing_rules)

        # 7. 新增：基于单元格边框样式生成CSS
        if table_model:
            border_rules = self._generate_border_rules_from_cells(table_model, config)
            if border_rules:
                css_parts.append(border_rules)
        else:
            # 回退到原有的边框模式样式（兼容性）
            border_rules = self._generate_border_mode_rules(config)
            if border_rules:
                css_parts.append(border_rules)

        # 8. 新增：合并单元格对齐优化CSS规则
        if table_model:
            merged_cell_alignment_rules = self._generate_merged_cell_alignment_rules(config, table_model)
            if merged_cell_alignment_rules:
                css_parts.append(merged_cell_alignment_rules)

        # 9. V4.0新增：内容溢出处理CSS规则
        overflow_rules = self._generate_overflow_rules(config.overflow_strategy)
        if overflow_rules:
            css_parts.append(overflow_rules)

        return "\n".join(css_parts)
    
    def _generate_font_face_rules(self, font_config) -> str:
        """
        生成@font-face CSS规则（V3.2增强版本）

        Args:
            font_config: 字体配置

        Returns:
            @font-face规则字符串
        """
        rules = []

        # V3.2：支持多个字体目录
        font_dirs = font_config.font_dirs
        if isinstance(font_dirs, str):
            font_dirs = [font_dirs]

        for font_dir_path in font_dirs:
            font_dir = Path(font_dir_path)

            if not font_dir.exists():
                self.logger.warning(f"字体目录不存在: {font_dir}")
                continue

            # 扫描TTF字体文件
            for font_file in font_dir.glob('*.ttf'):
                font_name = font_file.stem
                font_url = font_file.absolute().as_uri()
                rule = f"""@font-face {{
    font-family: '{font_name}';
    src: url('{font_url}');
}}"""
                rules.append(rule)

            # 扫描OTF字体文件
            for font_file in font_dir.glob('*.otf'):
                font_name = font_file.stem
                font_url = font_file.absolute().as_uri()
                rule = f"""@font-face {{
    font-family: '{font_name}';
    src: url('{font_url}');
}}"""
                rules.append(rule)

        return "\n".join(rules)
    
    def _generate_global_rules(self) -> str:
        """
        生成全局CSS规则
        
        Returns:
            全局CSS规则字符串
        """
        return """
table {
    border-collapse: collapse;
    margin: 20px;
}
"""

    def _generate_header_rules(self, header_config, transparency_config=None) -> str:
        """
        生成表头CSS规则

        Args:
            header_config: 解析后的表头样式参数
            transparency_config: V4.3新增：透明度配置参数

        Returns:
            表头CSS规则字符串
        """
        # V3.1：所有值都已经是具体的、确定性的
        default_font = header_config.font.default_family
        font_size = header_config.font.default_size
        # V3.4：padding智能调整将在CSS生成时通过box-sizing处理
        padding = header_config.padding
        text_align = header_config.horizontal_align
        vertical_align = header_config.vertical_align
        text_color = header_config.text_color
        background_color = header_config.background_color

        # V4.3新增：处理透明度
        text_shadow = ""
        if transparency_config and transparency_config.enable_transparency:
            # 导入透明度工具
            from ..utils.transparency_utils import TransparencyUtils
            transparency_utils = TransparencyUtils()

            # 根据颜色类型选择合适的透明度
            transparency_level = transparency_utils.get_appropriate_transparency(
                background_color, transparency_config
            )

            # 将背景色转换为透明格式
            background_color = transparency_utils.color_to_rgba(
                background_color, transparency_level
            )

            # 优化文字颜色以确保可读性
            text_color = transparency_utils.optimize_text_color_for_background(
                text_color, background_color
            )

            # 生成文字阴影以增强可读性
            shadow_css = transparency_utils.generate_text_shadow_css(text_color, background_color)
            text_shadow = f"text-shadow: {shadow_css};"

        return f"""
thead th {{
    font-family: '{default_font}', sans-serif;
    font-size: {font_size}px;
    padding: {padding}px;
    text-align: {text_align};
    vertical-align: {vertical_align};
    color: {text_color};
    background-color: {background_color};
    font-weight: bold;
    {text_shadow}
}}
"""

    def _generate_body_rules(self, body_config, transparency_config=None) -> str:
        """
        生成主体CSS规则

        Args:
            body_config: 解析后的主体样式参数
            transparency_config: V4.3新增：透明度配置参数

        Returns:
            主体CSS规则字符串
        """
        # V3.1：所有值都已经是具体的、确定性的
        default_font = body_config.font.default_family
        font_size = body_config.font.default_size
        # V3.4：padding智能调整将在CSS生成时通过box-sizing处理
        padding = body_config.padding
        text_align = body_config.horizontal_align
        vertical_align = body_config.vertical_align
        text_color = body_config.text_color
        background_color = body_config.background_color

        # V4.3新增：处理透明度
        text_shadow = ""
        if transparency_config and transparency_config.enable_transparency:
            # 导入透明度工具
            from ..utils.transparency_utils import TransparencyUtils
            transparency_utils = TransparencyUtils()

            # 根据颜色类型选择合适的透明度
            transparency_level = transparency_utils.get_appropriate_transparency(
                background_color, transparency_config
            )

            # 将背景色转换为透明格式
            background_color = transparency_utils.color_to_rgba(
                background_color, transparency_level
            )

            # 优化文字颜色以确保可读性
            text_color = transparency_utils.optimize_text_color_for_background(
                text_color, background_color
            )

            # 生成文字阴影以增强可读性
            shadow_css = transparency_utils.generate_text_shadow_css(text_color, background_color)
            text_shadow = f"text-shadow: {shadow_css};"

        return f"""
tbody td {{
    font-family: '{default_font}', sans-serif;
    font-size: {font_size}px;
    padding: {padding}px;
    text-align: {text_align};
    vertical-align: {vertical_align};
    color: {text_color};
    background-color: {background_color};
    {text_shadow}
}}
"""
    
    def _generate_zebra_stripes_rules(self, body_config, zebra_colors, transparency_config=None) -> str:
        """
        生成斑马条纹CSS规则

        Args:
            body_config: 表体样式配置
            zebra_colors: 斑马纹颜色列表 [奇数行背景色, 偶数行背景色]
            transparency_config: V4.3新增：透明度配置参数

        Returns:
            斑马条纹CSS规则字符串
        """
        if not zebra_colors or len(zebra_colors) < 2:
            return "tbody tr:nth-child(even) { background-color: #f2f2f2; }"

        # 获取表体的其他样式属性
        default_font = body_config.font.default_family
        font_size = body_config.font.default_size
        padding = body_config.padding
        text_align = body_config.horizontal_align
        vertical_align = body_config.vertical_align
        text_color = body_config.text_color

        odd_bg_color = zebra_colors[0]   # 奇数行背景色
        even_bg_color = zebra_colors[1]  # 偶数行背景色

        # V4.3新增：处理透明度
        text_shadow = ""
        if transparency_config and transparency_config.enable_transparency:
            # 导入透明度工具
            from ..utils.transparency_utils import TransparencyUtils
            transparency_utils = TransparencyUtils()

            # 分别根据颜色类型选择合适的透明度
            odd_transparency_level = transparency_utils.get_appropriate_transparency(
                odd_bg_color, transparency_config
            )
            even_transparency_level = transparency_utils.get_appropriate_transparency(
                even_bg_color, transparency_config
            )

            # 将背景色转换为透明格式
            odd_bg_color = transparency_utils.color_to_rgba(
                odd_bg_color, odd_transparency_level
            )
            even_bg_color = transparency_utils.color_to_rgba(
                even_bg_color, even_transparency_level
            )

            # 优化文字颜色（基于奇数行背景色）
            text_color = transparency_utils.optimize_text_color_for_background(
                text_color, odd_bg_color
            )

            # 生成文字阴影
            shadow_css = transparency_utils.generate_text_shadow_css(text_color, odd_bg_color)
            text_shadow = f"text-shadow: {shadow_css};"

        return f"""
/* 斑马条纹样式 */
tbody td {{
    font-family: '{default_font}', sans-serif;
    font-size: {font_size}px;
    padding: {padding}px;
    text-align: {text_align};
    vertical-align: {vertical_align};
    color: {text_color};
    {text_shadow}
}}

tbody tr:nth-child(odd) td {{
    background-color: {odd_bg_color};
}}

tbody tr:nth-child(even) td {{
    background-color: {even_bg_color};
}}
"""
    


    def _generate_hierarchical_rules(self, hierarchical_config) -> str:
        """
        生成分层样式CSS规则

        Args:
            hierarchical_config: 分层样式配置

        Returns:
            分层样式CSS规则字符串
        """
        rules = []

        # 生成列级样式
        if hierarchical_config.column_styles:
            for col_index, style_override in hierarchical_config.column_styles.items():
                css_rule = self._generate_style_override_rule(f".col-{col_index}", style_override)
                if css_rule:
                    rules.append(css_rule)

        # 生成行级样式
        if hierarchical_config.row_styles:
            for row_index, style_override in hierarchical_config.row_styles.items():
                css_rule = self._generate_style_override_rule(f".row-{row_index}", style_override)
                if css_rule:
                    rules.append(css_rule)

        # 生成单元格级样式
        if hierarchical_config.cell_styles:
            for cell_id, style_override in hierarchical_config.cell_styles.items():
                css_rule = self._generate_style_override_rule(f"#{cell_id}", style_override)
                if css_rule:
                    rules.append(css_rule)

        return "\n".join(rules)

    def _generate_style_override_rule(self, selector: str, style_override) -> str:
        """
        生成样式覆盖规则

        Args:
            selector: CSS选择器
            style_override: 样式覆盖配置

        Returns:
            CSS规则字符串
        """
        properties = []

        if style_override.font_family:
            properties.append(f"font-family: '{style_override.font_family}', sans-serif;")
        if style_override.font_size:
            properties.append(f"font-size: {style_override.font_size}px;")
        if style_override.font_bold is not None:
            weight = "bold" if style_override.font_bold else "normal"
            properties.append(f"font-weight: {weight};")
        if style_override.font_italic is not None:
            style = "italic" if style_override.font_italic else "normal"
            properties.append(f"font-style: {style};")
        if style_override.text_color:
            properties.append(f"color: {style_override.text_color};")
        if style_override.background_color:
            properties.append(f"background-color: {style_override.background_color};")
        if style_override.horizontal_align:
            properties.append(f"text-align: {style_override.horizontal_align};")
        if style_override.vertical_align:
            properties.append(f"vertical-align: {style_override.vertical_align};")

        if not properties:
            return ""

        return f"{selector} {{\n    " + "\n    ".join(properties) + "\n}"

    def _generate_sizing_rules(self, sizing: ResolvedSizingParams) -> str:
        """
        生成行列尺寸CSS规则

        Args:
            sizing: 解析后的sizing参数

        Returns:
            尺寸CSS规则字符串
        """
        rules = []

        # 添加调试日志
        self.logger.info(f"生成sizing规则，行高配置: {sizing.row_heights}")
        self.logger.info(f"生成sizing规则，列宽配置: {sizing.col_widths}")

        if not sizing.row_heights and not sizing.col_widths:
            self.logger.warning("没有特定的行高或列宽配置，只生成默认规则")

        # V3.4：生成特定行的高度规则 - 修复：应用到单元格，提高特异性
        for row_idx, height in sizing.row_heights.items():
            if isinstance(height, int):
                # 使用更高特异性的选择器确保覆盖thead th和tbody td规则
                rule = f"table .row-{row_idx} td, table .row-{row_idx} th {{ height: {height}px !important; }}"
                rules.append(rule)
                self.logger.info(f"生成行高CSS规则: {rule}")
            else:
                rule = f"table .row-{row_idx} td, table .row-{row_idx} th {{ height: {height} !important; }}"
                rules.append(rule)
                self.logger.info(f"生成行高CSS规则: {rule}")

        # V3.4：生成特定列的宽度规则 - 修复：应用到单元格，提高特异性
        for col_idx, width in sizing.col_widths.items():
            if isinstance(width, int):
                rule = f"table .col-{col_idx} {{ width: {width}px !important; }}"
                rules.append(rule)
                self.logger.info(f"生成列宽CSS规则: {rule}")
            else:
                rule = f"table .col-{col_idx} {{ width: {width} !important; }}"
                rules.append(rule)
                self.logger.info(f"生成列宽CSS规则: {rule}")

        # 生成默认行高规则 - 修复：应用到单元格，提高特异性
        if isinstance(sizing.default_row_height, int):
            rules.append(f"table .default-row td, table .default-row th {{ height: {sizing.default_row_height}px; }}")
        else:
            rules.append(f"table .default-row td, table .default-row th {{ height: {sizing.default_row_height}; }}")

        # 生成默认列宽规则 - 提高特异性
        if isinstance(sizing.default_col_width, int):
            rules.append(f"table .default-col {{ width: {sizing.default_col_width}px; }}")
        else:
            rules.append(f"table .default-col {{ width: {sizing.default_col_width}; }}")

        # V3.4：添加CSS盒模型设置以避免padding冲突
        rules.append("td, th { box-sizing: border-box; }")

        return "\n".join(rules)

    def _adjust_padding_for_sizing(self, padding: int, col_width: Union[str, int], row_height: Union[str, int]) -> int:
        """
        智能调整padding以避免与固定尺寸冲突

        Args:
            padding: 原始padding值
            col_width: 列宽配置
            row_height: 行高配置

        Returns:
            调整后的padding值
        """
        adjusted_padding = padding

        # 检查列宽冲突
        if isinstance(col_width, int) and col_width != 0:
            min_content_width = 20  # 最小内容区域宽度
            max_safe_padding = (col_width - min_content_width) // 2
            if max_safe_padding > 0 and padding > max_safe_padding:
                adjusted_padding = max_safe_padding
                self.logger.warning(f"列宽{col_width}px与padding {padding}px冲突，自动调整padding为{adjusted_padding}px以保证最小内容区域")

        # 检查行高冲突
        if isinstance(row_height, int) and row_height != 0:
            min_content_height = 16  # 最小内容区域高度（考虑字体大小）
            max_safe_padding_height = (row_height - min_content_height) // 2
            if max_safe_padding_height > 0 and adjusted_padding > max_safe_padding_height:
                adjusted_padding = max_safe_padding_height
                self.logger.warning(f"行高{row_height}px与padding {padding}px冲突，自动调整padding为{adjusted_padding}px以保证最小内容区域")

        return max(1, adjusted_padding)  # 确保padding至少为1px
    
    def _get_random_choice(self, value: Union[Any, List[Any]]) -> Any:
        """
        从值或值列表中随机选择一个
        
        Args:
            value: 单个值或值列表
            
        Returns:
            随机选择的值
        """
        if isinstance(value, list):
            return self.random_state.choice(value)
        return value
    
    def _get_randomized_value(self, value: Union[int, RangeConfig]) -> int:
        """
        从固定值或范围中获取随机值

        Args:
            value: 固定值或范围配置

        Returns:
            随机值
        """
        if isinstance(value, RangeConfig):
            return self.random_state.randint(value.min, value.max + 1)
        return value

    def _generate_border_mode_rules(self, config: ResolvedStyleParams) -> str:
        """
        生成边框模式CSS规则（V3.2新增）

        Args:
            config: 解析后的样式参数

        Returns:
            边框模式CSS规则字符串
        """
        if config.border_mode == 'full':
            # 有线模式：所有边框都显示
            return """
/* 有线模式：完整边框 */
table { border-collapse: collapse; }
th, td {
    border: 1px solid #000000;
    box-sizing: border-box;
}
"""
        elif config.border_mode == 'none':
            # 纯无线模式：没有边框
            return """
/* 纯无线模式：无边框 */
table { border-collapse: collapse; }
th, td { border: none; }
"""
        elif config.border_mode == 'semi' and config.border_details:
            # 半无线模式：根据概率显示边框
            return self._generate_semi_border_rules(config.border_details)
        else:
            # 默认使用有线模式
            return """
/* 默认模式：完整边框 */
table { border-collapse: collapse; }
th, td {
    border: 1px solid #000000;
    box-sizing: border-box;
}
"""

    def _generate_semi_border_rules(self, border_details: Dict[str, Any]) -> str:
        """
        生成半无线边框规则

        Args:
            border_details: 边框详细配置

        Returns:
            半无线边框CSS规则字符串
        """
        rules = ["/* 半无线模式：概率性边框 */", "table { border-collapse: collapse; }"]

        # 基础样式：默认无边框
        rules.append("th, td { border: none; }")

        # 外框边框
        if border_details.get('outer_frame', True):
            rules.append("""
/* 外框边框 */
table { border: 1px solid #000000; }
""")

        # 表头分割线：移除强制CSS规则，改为通过单元格样式智能处理
        # 原强制规则已移除，表头分割线现在完全由颜色差异检测和单元格边框样式控制
        # 这确保了渲染效果与标注结果的一致性

        # 行线概率控制
        row_prob = border_details.get('row_line_probability', 0.5)
        if row_prob > 0:
            # 生成多个行线类，用于随机应用
            for i in range(10):
                if self.random_state.random() < row_prob:
                    rules.append(f".row-border-{i} {{ border-bottom: 1px solid #000000; }}")

        # 列线概率控制
        col_prob = border_details.get('col_line_probability', 0.5)
        if col_prob > 0:
            # 生成多个列线类，用于随机应用
            for i in range(10):
                if self.random_state.random() < col_prob:
                    rules.append(f".col-border-{i} {{ border-right: 1px solid #000000; }}")

        return "\n".join(rules)

    def _generate_border_rules_from_decisions(self, border_decisions, table_model=None) -> str:
        """
        基于边框决策生成CSS规则

        Args:
            border_decisions: 边框决策对象
            table_model: 表格模型（用于获取表头/主体分离信息）

        Returns:
            边框CSS规则字符串
        """
        rules = ["/* 基于边框决策的样式 */", "table { border-collapse: collapse; }"]

        # 基础样式：默认无边框
        rules.append("th, td { border: none; }")

        # 外框边框
        if border_decisions.outer_frame:
            rules.append("""
/* 外框边框 */
table { border: 1px solid #000000; }
""")

        # 表头分割线（通过行边框实现，这里不需要额外的CSS）
        # 表头分割线已经通过row_borders中的设置来实现

        # 行边框：为每个需要显示边框的行生成CSS规则
        for row_idx, show_border in border_decisions.row_borders.items():
            if show_border:
                rules.append(f".row-{row_idx} {{ border-bottom: 1px solid #000000; }}")

        # 列边框：为每个需要显示边框的列生成CSS规则
        for col_idx, show_border in border_decisions.col_borders.items():
            if show_border:
                rules.append(f".col-{col_idx} {{ border-right: 1px solid #000000; }}")

        return "\n".join(rules)

    def _generate_border_rules_from_cells(self, table_model, config=None) -> str:
        """
        基于单元格边框样式生成CSS规则

        V4.5增强：支持边框颜色生成，直接为每个单元格生成对应的边框CSS。

        Args:
            table_model: 表格模型（包含单元格边框样式）
            config: 样式配置（V4.5新增）

        Returns:
            边框CSS规则字符串
        """
        rules = ["/* V4.5: 基于单元格边框样式的CSS（支持边框颜色） */", "table { border-collapse: collapse; }"]

        # 基础样式：默认无边框
        rules.append("""th, td {
            border: none;
            box-sizing: border-box;
        }""")

        # V4.5修正：先生成实际的单元格颜色，然后基于实际颜色进行差异检测，但不修改边框状态
        if config:
            # 生成实际的单元格颜色映射
            actual_colors = self._generate_actual_cell_colors(table_model, config)

            # 基于实际颜色进行差异检测，生成颜色差异边框映射
            color_diff_borders = self.color_diff_detector.detect_color_based_borders_with_actual_colors(table_model, actual_colors)

            # 将颜色差异映射保存到表格模型中，供标注生成使用
            table_model.color_diff_borders = color_diff_borders

            # V4.5新增：为每个单元格生成边框颜色（如果启用）
            self.border_color_manager.generate_border_colors_for_cells(table_model, config, self.random_state)

        # 为每个单元格生成特定的边框CSS
        for row in table_model.rows:
            # V5.1修复：检查行是否有单元格，避免空行导致的问题
            if not row.cells:
                self.logger.warning(f"[STYLE_BUILDER] 发现空行，跳过边框处理: row_index={row.row_index}")
                continue
            for cell in row.cells:
                if (cell.border_style.top or cell.border_style.right or
                    cell.border_style.bottom or cell.border_style.left):

                    # V4.5增强：构建带颜色的边框样式
                    border_parts = []
                    if cell.border_style.top:
                        border_parts.append(f"border-top: 1px solid {cell.border_style.top_color}")
                    if cell.border_style.right:
                        border_parts.append(f"border-right: 1px solid {cell.border_style.right_color}")
                    if cell.border_style.bottom:
                        border_parts.append(f"border-bottom: 1px solid {cell.border_style.bottom_color}")
                    if cell.border_style.left:
                        border_parts.append(f"border-left: 1px solid {cell.border_style.left_color}")

                    if border_parts:
                        css_rule = f"#{cell.cell_id} {{ {'; '.join(border_parts)}; }}"
                        rules.append(css_rule)

        return "\n".join(rules)

    def _generate_actual_cell_colors(self, table_model, config):
        """
        V4.5改进：生成实际的单元格颜色映射

        这个方法模拟实际的颜色生成过程，考虑 randomize_color_probability 等因素，
        为每个单元格生成更接近实际的背景色，用于颜色差异检测。

        Args:
            table_model: 表格模型
            config: 样式配置

        Returns:
            字典：{cell_id: background_color}
        """
        actual_colors = {}

        # 获取颜色随机化概率（从原始配置中获取，因为这个值没有传递到 ResolvedStyleParams）
        # 这里使用一个合理的默认值，或者从配置中推断
        randomize_color_prob = 0.3  # 默认值，与配置中的默认值一致

        for row in table_model.rows:
            # V5.1修复：检查行是否有单元格，避免空行导致的问题
            if not row.cells:
                self.logger.warning(f"[STYLE_BUILDER] 发现空行，跳过颜色处理: row_index={row.row_index}")
                continue
            for cell in row.cells:
                if cell.is_header:
                    # 表头颜色：直接使用解析后的颜色
                    # 因为表头颜色在解析时已经考虑了随机化
                    actual_colors[cell.cell_id] = config.header.background_color
                else:
                    # 主体颜色：需要考虑颜色继承和随机化
                    # 简化处理：直接使用解析后的主体颜色
                    # 在实际应用中，这个颜色已经经过了随机化处理
                    actual_colors[cell.cell_id] = config.body.background_color

        # 处理斑马条纹的情况
        if config.zebra_stripes and config.zebra_colors:
            body_row_index = 0
            for row in table_model.rows:
                # V5.1修复：检查行是否有单元格，避免空行导致的索引错误
                if not row.cells:
                    self.logger.warning(f"[STYLE_BUILDER] 发现空行，跳过处理: row_index={row.row_index}")
                    continue

                if not row.cells[0].is_header:  # 只处理主体行
                    # 斑马条纹：奇偶行使用不同颜色
                    zebra_color = config.zebra_colors[body_row_index % 2]
                    for cell in row.cells:
                        if not cell.is_header:
                            actual_colors[cell.cell_id] = zebra_color
                    body_row_index += 1

        return actual_colors

    def _generate_merged_cell_alignment_rules(self, config, table_model) -> str:
        """
        生成合并单元格对齐优化CSS规则

        为合并单元格应用相对居中的对齐规则：
        - horizontal_align为left/right时，vertical_align设为middle
        - vertical_align为top/bottom时，horizontal_align设为center
        - 冲突时随机选择应用哪个规则

        Args:
            config: 解析后的样式参数
            table_model: 表格模型

        Returns:
            合并单元格对齐优化CSS规则字符串
        """
        # 获取合并单元格居中概率配置
        merged_cell_center_probability = self._get_merged_cell_center_probability(config)

        if merged_cell_center_probability <= 0:
            return ""  # 概率为0，不生成规则

        rules = ["/* 合并单元格对齐优化 */"]

        for row in table_model.rows:
            # V5.1修复：检查行是否有单元格，避免空行导致的问题
            if not row.cells:
                self.logger.warning(f"[STYLE_BUILDER] 发现空行，跳过合并单元格处理: row_index={row.row_index}")
                continue
            for cell in row.cells:
                # 只处理合并单元格
                if not (cell.row_span > 1 or cell.col_span > 1):
                    continue

                # 概率控制
                if self.random_state.random() > merged_cell_center_probability:
                    continue

                # 获取当前单元格的对齐方式
                current_h_align, current_v_align = self._get_cell_alignment(config, cell)

                # 应用对齐优化规则
                optimized_h_align, optimized_v_align = self._apply_alignment_optimization(
                    current_h_align, current_v_align
                )

                # 如果对齐方式有变化，生成CSS规则
                if (optimized_h_align != current_h_align or optimized_v_align != current_v_align):
                    alignment_parts = []
                    if optimized_h_align != current_h_align:
                        alignment_parts.append(f"text-align: {optimized_h_align}")
                    if optimized_v_align != current_v_align:
                        alignment_parts.append(f"vertical-align: {optimized_v_align}")

                    if alignment_parts:
                        css_rule = f"#{cell.cell_id} {{ {'; '.join(alignment_parts)}; }}"
                        rules.append(css_rule)

        return "\n".join(rules) if len(rules) > 1 else ""

    def _get_merged_cell_center_probability(self, config) -> float:
        """
        获取合并单元格居中概率配置

        Args:
            config: 解析后的样式参数

        Returns:
            合并单元格居中概率
        """
        return getattr(config, 'merged_cell_center_probability', 0.0)

    def _get_cell_alignment(self, config, cell):
        """
        获取单元格的当前对齐方式

        考虑分层样式的优先级：分层样式 > 基础样式

        Args:
            config: 解析后的样式参数
            cell: 单元格模型

        Returns:
            (horizontal_align, vertical_align) 元组
        """
        # 默认对齐方式（基于单元格是否为表头）
        if cell.is_header:
            base_h_align = config.header.horizontal_align
            base_v_align = config.header.vertical_align
        else:
            base_h_align = config.body.horizontal_align
            base_v_align = config.body.vertical_align

        # 检查分层样式覆盖
        if config.hierarchical:
            # 检查单元格级别的样式覆盖
            if config.hierarchical.cell_styles and cell.cell_id in config.hierarchical.cell_styles:
                cell_style = config.hierarchical.cell_styles[cell.cell_id]
                if cell_style.horizontal_align:
                    base_h_align = cell_style.horizontal_align
                if cell_style.vertical_align:
                    base_v_align = cell_style.vertical_align

            # 检查行级别的样式覆盖
            elif config.hierarchical.row_styles and cell.row_index in config.hierarchical.row_styles:
                row_style = config.hierarchical.row_styles[cell.row_index]
                if row_style.horizontal_align:
                    base_h_align = row_style.horizontal_align
                if row_style.vertical_align:
                    base_v_align = row_style.vertical_align

            # 检查列级别的样式覆盖
            elif config.hierarchical.column_styles and cell.col_index in config.hierarchical.column_styles:
                col_style = config.hierarchical.column_styles[cell.col_index]
                if col_style.horizontal_align:
                    base_h_align = col_style.horizontal_align
                if col_style.vertical_align:
                    base_v_align = col_style.vertical_align

        return base_h_align, base_v_align

    def _apply_alignment_optimization(self, horizontal_align: str, vertical_align: str) -> tuple:
        """
        应用合并单元格对齐优化规则

        规则：
        1. horizontal_align为left/right时，vertical_align设为middle
        2. vertical_align为top/bottom时，horizontal_align设为center
        3. 冲突时随机选择应用哪个规则
        4. 已经符合规则的情况无需处理

        Args:
            horizontal_align: 当前水平对齐方式
            vertical_align: 当前垂直对齐方式

        Returns:
            (优化后的horizontal_align, 优化后的vertical_align) 元组
        """
        # 检查是否需要优化
        h_needs_optimization = horizontal_align in ['left', 'right']
        v_needs_optimization = vertical_align in ['top', 'bottom']

        # 如果都不需要优化，直接返回
        if not h_needs_optimization and not v_needs_optimization:
            return horizontal_align, vertical_align

        # 如果已经是center或middle，无需处理
        if horizontal_align == 'center' or vertical_align == 'middle':
            return horizontal_align, vertical_align

        # 应用优化规则
        optimized_h = horizontal_align
        optimized_v = vertical_align

        if h_needs_optimization and v_needs_optimization:
            # 冲突情况：随机选择应用哪个规则
            if self.random_state.random() < 0.5:
                # 应用规则1：水平left/right → 垂直middle
                optimized_v = 'middle'
            else:
                # 应用规则2：垂直top/bottom → 水平center
                optimized_h = 'center'
        elif h_needs_optimization:
            # 只有水平需要优化：left/right → vertical middle
            optimized_v = 'middle'
        elif v_needs_optimization:
            # 只有垂直需要优化：top/bottom → horizontal center
            optimized_h = 'center'

        return optimized_h, optimized_v

    def _generate_overflow_rules(self, overflow_strategy: str) -> str:
        """
        V4.0新增：生成内容溢出处理CSS规则

        Args:
            overflow_strategy: 溢出处理策略 ('truncate' 或 'wrap')

        Returns:
            CSS规则字符串
        """
        if overflow_strategy == 'truncate':
            # 截断策略：不换行，隐藏溢出，显示省略号
            return """
/* V4.0 内容溢出处理：截断策略 */
th, td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
"""
        elif overflow_strategy == 'wrap':
            # 换行策略：允许换行，自动断词
            return """
/* V4.0 内容溢出处理：换行策略 */
th, td {
    word-wrap: break-word;
    white-space: normal;
    overflow-wrap: break-word;
}
"""
        else:
            # 未知策略，返回空字符串
            self.logger.warning(f"未知的溢出处理策略: {overflow_strategy}")
            return ""

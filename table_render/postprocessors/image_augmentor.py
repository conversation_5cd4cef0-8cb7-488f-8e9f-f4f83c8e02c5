"""
图像增强器实现

V4.0新增：实现具体的图像后处理效果，包括透视变换、背景合成等。
V4.5更新：移除旧版本的模糊和噪声效果，统一使用降质效果系统。
"""

import io
import logging
import numpy as np
import cv2
import os
import json
from pathlib import Path
from PIL import Image, ImageDraw
from typing import Optional, Dict, Any, Tuple

from .base_augmentor import BaseAugmentor
from .background_composer import BackgroundComposer  # V5.3保留仅为兼容性，实际已废弃
from .degradation_processor import DegradationProcessor
from ..config import ResolvedPostprocessingParams
from ..utils.performance_profiler import profile_stage


class ImageAugmentor(BaseAugmentor):
    """
    图像增强器实现类

    提供透视变换、背景合成、降质效果等图像后处理功能。
    """
    
    def __init__(self, seed: int, debug_mode: bool = False, debug_output_dir: Optional[str] = None):
        """
        初始化图像增强器

        Args:
            seed: 随机种子，用于确保可复现性
            debug_mode: 是否启用调试模式
            debug_output_dir: 调试输出目录
        """
        self.random_state = np.random.RandomState(seed)
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        self.debug_output_dir = debug_output_dir
        self.debug_stage_counter = 0

        # V4.5新增：初始化降质处理器
        try:
            self.degradation_processor = DegradationProcessor(seed)
        except Exception as e:
            self.logger.error(f"DegradationProcessor 创建失败: {e}")
            self.degradation_processor = None

        if self.debug_mode and self.debug_output_dir:
            # 确保调试输出目录存在
            Path(self.debug_output_dir).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"ImageAugmentor调试模式已启用，输出目录: {self.debug_output_dir}")

    def _save_debug_stage(self, stage_name: str, image: Image.Image, annotations: Optional[Dict[str, Any]],
                         additional_info: Optional[Dict[str, Any]] = None):
        """
        保存调试阶段的图像和标注

        Args:
            stage_name: 阶段名称
            image: 图像
            annotations: 标注数据
            additional_info: 额外信息
        """
        if not self.debug_mode or not self.debug_output_dir:
            return

        self.debug_stage_counter += 1
        stage_prefix = f"stage{self.debug_stage_counter:02d}_{stage_name}"

        # 保存图像
        image_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.png")
        image.save(image_path)
        self.logger.info(f"调试: 保存图像 {image_path}")

        # 保存标注
        if annotations is not None:
            annotation_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.json")
            debug_data = {
                "stage": stage_name,
                "image_size": image.size,
                "annotations": annotations
            }
            if additional_info:
                debug_data.update(additional_info)

            # 确保数据可以JSON序列化
            serializable_debug_data = self._make_json_serializable(debug_data)
            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_debug_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"调试: 保存标注 {annotation_path}")

        # 生成可视化图像（带标注框）
        if annotations and annotations.get('cells'):
            vis_image = self._create_visualization_image(image, annotations)
            vis_path = os.path.join(self.debug_output_dir, f"{stage_prefix}_with_boxes.png")
            vis_image.save(vis_path)
            self.logger.info(f"调试: 保存可视化图像 {vis_path}")

    def _create_visualization_image(self, image: Image.Image, annotations: Dict[str, Any]) -> Image.Image:
        """
        创建带标注框的可视化图像

        Args:
            image: 原始图像
            annotations: 标注数据

        Returns:
            带标注框的图像
        """
        vis_image = image.copy()
        draw = ImageDraw.Draw(vis_image)

        # 定义颜色
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

        for i, cell in enumerate(annotations.get('cells', [])):
            if 'bbox' not in cell:
                continue

            bbox = cell['bbox']
            color = colors[i % len(colors)]

            if isinstance(bbox, dict) and all(key in bbox for key in ['p1', 'p2', 'p3', 'p4']):
                # 四角点格式
                points = [tuple(bbox['p1']), tuple(bbox['p2']), tuple(bbox['p3']), tuple(bbox['p4'])]
                # 绘制多边形
                draw.polygon(points, outline=color, width=2)
                # 标记单元格编号
                draw.text(bbox['p1'], str(i), fill=color)
            elif isinstance(bbox, list) and len(bbox) >= 4:
                # 矩形格式 [x_min, y_min, x_max, y_max]
                x_min, y_min, x_max, y_max = bbox[:4]
                draw.rectangle([x_min, y_min, x_max, y_max], outline=color, width=2)
                # 标记单元格编号
                draw.text((x_min, y_min), str(i), fill=color)

        return vis_image
    
    def process(self,
               image_bytes: bytes,
               annotations: Optional[Dict[str, Any]] = None,
               params: Optional[ResolvedPostprocessingParams] = None) -> Tuple[bytes, Optional[Dict[str, Any]]]:
        """
        处理图像字节数据和标注

        Args:
            image_bytes: 原始图像的字节数据
            annotations: 最终格式的标注数据（包含p1,p2,p3,p4格式），如果为None则不处理标注
            params: 解析后的后处理参数，如果为None则不进行任何处理

        Returns:
            (处理后的图像字节数据, 处理后的标注数据)
        """
        with profile_stage("image_bytes_to_pil", {
            "image_size_bytes": len(image_bytes)
        }):
            # 将字节数据转换为PIL图像
            image = Image.open(io.BytesIO(image_bytes))

        # 调试模式：即使没有后处理参数也要保存原始状态
        if self.debug_mode and self.debug_output_dir:
            self._save_debug_stage("original_input", image, annotations, {
                "stage_description": "后处理模块接收到的原始输入"
            })

        if params is None:
            # 调试模式：保存无处理的输出状态
            if self.debug_mode and self.debug_output_dir:
                self._save_debug_stage("no_processing", image, annotations, {
                    "stage_description": "无后处理参数，直接输出"
                })
            return image_bytes, annotations

        # 应用增强效果
        with profile_stage("image_augmentation", {
            "image_size": f"{image.width}x{image.height}",
            "has_perspective": getattr(params, 'apply_perspective', False),
            "has_background": getattr(params, 'apply_background', False),
            "has_degradation": any([
                getattr(params, 'apply_degradation_blur', False),
                getattr(params, 'apply_degradation_noise', False),
                getattr(params, 'apply_degradation_fade_global', False)
            ])
        }):
            enhanced_image, enhanced_annotations = self.augment(image, annotations, params)

        with profile_stage("pil_to_image_bytes"):
            # 将处理后的图像转换回字节数据
            output_buffer = io.BytesIO()
            enhanced_image.save(output_buffer, format='PNG')
            return output_buffer.getvalue(), enhanced_annotations
    
    def augment(self,
               image: Image.Image,
               annotations: Optional[Dict[str, Any]] = None,
               params: ResolvedPostprocessingParams = None) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        对PIL图像对象和标注进行增强处理

        Args:
            image: PIL图像对象
            annotations: 最终格式的标注数据
            params: 解析后的后处理参数

        Returns:
            (增强后的PIL图像对象, 增强后的标注数据)
        """
        import copy
        enhanced_image = image.copy()
        enhanced_annotations = copy.deepcopy(annotations) if annotations is not None else None

        # 调试模式：保存原始状态
        if self.debug_mode and self.debug_output_dir:
            self._save_debug_stage("postprocess_input", enhanced_image, enhanced_annotations, {
                "stage_description": "图像后处理输入状态"
            })

        # 按顺序应用各种效果
        if params.apply_perspective and params.perspective_offset_ratio is not None:
            shrink_ratio = getattr(params, 'content_area_shrink_ratio', 0.1)
            enhanced_image, enhanced_annotations = self._apply_perspective_transform_with_annotations(
                enhanced_image, enhanced_annotations, params.perspective_offset_ratio, shrink_ratio
            )
            self.logger.debug(f"应用透视变换，偏移比例: {params.perspective_offset_ratio}, 缩小比例: {shrink_ratio}")

        # V5.3架构简化：移除后处理背景合成，统一使用CSS背景模式
        # 所有背景处理都在CSS阶段完成，获得最佳质量和完整的5行5列扩展支持
        if params.apply_background and params.background_image_path is not None:
            self.logger.info(f"[UNIFIED_BACKGROUND] 背景处理已在CSS阶段完成")
            self.logger.info(f"[UNIFIED_BACKGROUND] CSS背景尺寸: {params.css_background_width}x{params.css_background_height}")
            self.logger.info(f"[UNIFIED_BACKGROUND] 背景图: {os.path.basename(params.background_image_path)}")
            self.logger.info(f"[UNIFIED_BACKGROUND] 后处理背景合成已废弃，使用CSS模式获得更好效果")

            # 验证CSS背景是否正确应用
            if not (hasattr(params, 'css_background_applied') and params.css_background_applied):
                self.logger.error(f"[UNIFIED_BACKGROUND] 错误：CSS背景未正确应用，但后处理背景合成已废弃")
                self.logger.error(f"[UNIFIED_BACKGROUND] 请检查resolver配置或联系开发者")
                # 不再进行后处理背景合成，避免双重背景问题

        # V4.5新增：应用降质效果（在所有其他后处理之后）
        if (params.apply_degradation_blur or params.apply_degradation_noise or
            params.apply_degradation_fade_global or params.apply_degradation_fade_local or
            params.apply_degradation_uneven_lighting or params.apply_degradation_jpeg or
            params.apply_degradation_darker_brighter or params.apply_degradation_gamma_correction):

            self.logger.debug("开始应用降质效果")

            # 检查降质处理器是否可用
            if self.degradation_processor is None:
                self.logger.error("降质处理器未初始化，跳过降质处理")
            else:
                # 调试模式：保存降质前状态
                if self.debug_mode and self.debug_output_dir:
                    self._save_debug_stage("before_degradation", enhanced_image, enhanced_annotations, {
                        "stage_description": "降质处理前状态"
                    })

                enhanced_image, enhanced_annotations = self.degradation_processor.apply_degradations(
                    enhanced_image, enhanced_annotations, params
                )

            # 调试模式：保存降质后状态
            if self.debug_mode and self.debug_output_dir:
                self._save_debug_stage("after_degradation", enhanced_image, enhanced_annotations, {
                    "stage_description": "降质处理后状态"
                })

            self.logger.debug("降质效果应用完成")

        # 调试模式：保存最终结果
        if self.debug_mode and self.debug_output_dir:
            self._save_debug_stage("postprocess_output", enhanced_image, enhanced_annotations, {
                "stage_description": "图像后处理最终输出"
            })

        return enhanced_image, enhanced_annotations
    

    def _apply_perspective_transform_with_annotations(self,
                                                    image: Image.Image,
                                                    annotations: Optional[Dict[str, Any]],
                                                    max_offset_ratio: float,
                                                    content_area_shrink_ratio: float = 0.1) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        应用透视变换效果并同时处理标注

        Args:
            image: PIL图像对象
            annotations: 最终格式的标注数据
            max_offset_ratio: 最大偏移比例（相对于图像短边）
            content_area_shrink_ratio: 内容区域缩小比例

        Returns:
            (透视变换后的图像, 变换后的标注数据)
        """
        # 保存缩小比例到实例变量，供后续方法使用
        self._content_area_shrink_ratio = content_area_shrink_ratio
        # 将PIL图像转换为OpenCV格式
        img_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        h, w = img_cv.shape[:2]

        # 计算最大偏移量
        max_offset = min(h, w) * max_offset_ratio

        # 定义原始图像的四个角点
        src_points = np.float32([
            [0, 0],           # 左上
            [w - 1, 0],       # 右上
            [0, h - 1],       # 左下
            [w - 1, h - 1]    # 右下
        ])

        # 为每个角点添加随机偏移，创建目标角点
        dst_points = np.float32([
            [
                self.random_state.uniform(-max_offset, max_offset),
                self.random_state.uniform(-max_offset, max_offset)
            ],  # 左上
            [
                w - 1 + self.random_state.uniform(-max_offset, max_offset),
                self.random_state.uniform(-max_offset, max_offset)
            ],  # 右上
            [
                self.random_state.uniform(-max_offset, max_offset),
                h - 1 + self.random_state.uniform(-max_offset, max_offset)
            ],  # 左下
            [
                w - 1 + self.random_state.uniform(-max_offset, max_offset),
                h - 1 + self.random_state.uniform(-max_offset, max_offset)
            ]   # 右下
        ])

        # 计算透视变换矩阵
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)

        # 应用透视变换
        warped_img = cv2.warpPerspective(img_cv, matrix, (w, h))

        # 转换回PIL图像格式
        warped_img_rgb = cv2.cvtColor(warped_img, cv2.COLOR_BGR2RGB)
        warped_image = Image.fromarray(warped_img_rgb)

        # 处理标注坐标变换
        transformed_annotations = self._transform_annotations(annotations, matrix)

        # V4.4.2新增：预测黑边区域并添加到标注中
        black_edge_info = self._predict_black_edges_from_transform_matrix(matrix, (w, h))

        # CSS模式增强：实际检测图像边缘异常区域
        actual_edge_info = self._detect_actual_edge_anomalies(warped_image)
        black_edge_info['actual_edge_detection'] = actual_edge_info

        if transformed_annotations is not None:
            transformed_annotations['black_edge_info'] = black_edge_info
            # 添加调试标记
            transformed_annotations['debug_perspective_applied'] = True
            transformed_annotations['debug_transform_matrix'] = matrix.tolist()

        # 调试模式：保存黑边预测信息和可视化
        if self.debug_mode and self.debug_output_dir:
            # 创建黑边可视化图像
            visualized_image = self._create_black_edge_visualization(warped_image, black_edge_info)

            self._save_debug_stage("perspective_with_black_edge_prediction", warped_image, transformed_annotations, {
                "stage_description": "透视变换完成，包含黑边预测信息",
                "black_edge_info": black_edge_info
            })

            # 单独保存黑边可视化
            self._save_debug_stage("black_edge_visualization", visualized_image, transformed_annotations, {
                "stage_description": "黑边区域可视化（红色=黑边，绿色=表格安全区域，蓝色=内容区域）",
                "black_edge_info": black_edge_info
            })

        return warped_image, transformed_annotations

    def _transform_annotations(self, annotations: Optional[Dict[str, Any]], perspective_matrix: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        将透视变换应用到标注坐标上

        Args:
            annotations: 最终格式的标注数据
            perspective_matrix: 透视变换矩阵

        Returns:
            变换后的标注数据
        """
        if annotations is None:
            return None

        # 深拷贝标注数据以避免修改原始数据
        import copy
        transformed_annotations = copy.deepcopy(annotations)

        # 对每个单元格的四个角点应用透视变换
        for i, cell in enumerate(transformed_annotations.get('cells', [])):
            bbox = cell.get('bbox', {})

            # 验证bbox格式
            required_points = ['p1', 'p2', 'p3', 'p4']
            if not all(point in bbox for point in required_points):
                self.logger.warning(f"单元格{i}的bbox缺少必要的角点，跳过透视变换: {bbox}")
                continue

            # 提取四个角点坐标
            corners = np.array([
                [bbox['p1']],
                [bbox['p2']],
                [bbox['p3']],
                [bbox['p4']]
            ], dtype=np.float32)

            self.logger.debug(f"透视变换前单元格{i}: {bbox}")

            # 应用透视变换
            transformed_corners = cv2.perspectiveTransform(corners, perspective_matrix)

            # 更新标注中的坐标
            new_bbox = {
                'p1': [float(transformed_corners[0][0][0]), float(transformed_corners[0][0][1])],
                'p2': [float(transformed_corners[1][0][0]), float(transformed_corners[1][0][1])],
                'p3': [float(transformed_corners[2][0][0]), float(transformed_corners[2][0][1])],
                'p4': [float(transformed_corners[3][0][0]), float(transformed_corners[3][0][1])]
            }
            cell['bbox'] = new_bbox
            self.logger.debug(f"透视变换后单元格{i}: {new_bbox}")

        self.logger.debug(f"已变换 {len(transformed_annotations.get('cells', []))} 个单元格的标注坐标")
        return transformed_annotations

    def _predict_black_edges_from_transform_matrix(self, matrix: np.ndarray, image_size: tuple) -> dict:
        """
        基于透视变换矩阵预测黑边区域

        Args:
            matrix: 透视变换矩阵
            image_size: 图像尺寸 (width, height)

        Returns:
            黑边信息字典，包含黑边区域和有效内容区域
        """
        w, h = image_size

        # 定义原图四个角点
        src_corners = np.array([
            [0, 0],           # 左上
            [w-1, 0],         # 右上
            [w-1, h-1],       # 右下
            [0, h-1]          # 左下
        ], dtype=np.float32)

        # 计算变换后的角点位置
        dst_corners = cv2.perspectiveTransform(src_corners.reshape(-1, 1, 2), matrix)
        dst_corners = dst_corners.reshape(-1, 2)

        # 计算变换后内容的边界框
        content_bbox = self._calculate_content_bounding_box(dst_corners, image_size)

        # 计算黑边区域
        black_edge_regions = self._calculate_black_edge_regions(content_bbox, image_size)

        # 计算表格安全区域（变换后表格内容的实际范围）
        table_safe_area = self._calculate_table_safe_area_from_transform(dst_corners, image_size)

        # V4.4.2简化：直接调整内接矩形以包含表格区域，然后应用缩小比例
        adjusted_content_bbox = self._simple_rect_adjustment_for_table(
            content_bbox, table_safe_area, image_size
        )

        # 应用内容区域缩小比例以更保守地避免黑边
        shrink_ratio = getattr(self, '_content_area_shrink_ratio', 0.1)  # 默认10%
        optimized_content_bbox = self._apply_content_area_shrink(
            adjusted_content_bbox, shrink_ratio
        )

        # 重新计算黑边区域（基于调整后的内容区域）
        black_edge_regions = self._calculate_black_edge_regions(optimized_content_bbox, image_size)

        black_edge_info = {
            'has_black_edges': bool(len(black_edge_regions) > 0),
            'content_bbox': tuple(int(x) for x in optimized_content_bbox),  # 确保为int类型
            'original_inscribed_rect': tuple(int(x) for x in content_bbox),  # 确保为int类型
            'black_edge_regions': [tuple(int(x) for x in region) for region in black_edge_regions],  # 确保为int类型
            'table_safe_area': tuple(int(x) for x in table_safe_area),  # 确保为int类型
            'transformed_corners': [[float(x), float(y)] for x, y in dst_corners],  # 确保为float类型
            'original_image_size': tuple(int(x) for x in image_size),
            'adjustment_applied': bool(optimized_content_bbox != content_bbox)  # 确保为bool类型
        }

        self.logger.info(f"[BLACK_EDGE_V442] 黑边预测完成:")
        self.logger.info(f"  - 算法版本: V4.4.2 简化边界调整算法")
        self.logger.info(f"  - 原始内接矩形: {content_bbox}")
        self.logger.info(f"  - 最终内容区域: {optimized_content_bbox}")
        self.logger.info(f"  - 表格区域调整: {black_edge_info['adjustment_applied']}")
        self.logger.info(f"  - 检测到黑边: {black_edge_info['has_black_edges']}")
        self.logger.info(f"  - 黑边区域数量: {len(black_edge_regions)}")

        return black_edge_info

    def _calculate_content_bounding_box(self, transformed_corners: np.ndarray, image_size: tuple) -> tuple:
        """
        V4.4.2改进：计算四边形的最大内接矩形，而不是外接矩形

        Args:
            transformed_corners: 变换后的四个角点
            image_size: 图像尺寸 (width, height)

        Returns:
            最大内接矩形 (left, top, right, bottom)
        """
        w, h = image_size

        # 调试信息：记录变换后的角点
        self.logger.debug(f"[MAX_INSCRIBED_RECT] 变换后角点: {transformed_corners.tolist()}")

        # 计算最大内接矩形
        max_inscribed_rect = self._find_max_inscribed_rectangle(transformed_corners, image_size)

        self.logger.debug(f"[MAX_INSCRIBED_RECT] 最大内接矩形: {max_inscribed_rect}")

        return max_inscribed_rect

    def _find_max_inscribed_rectangle(self, corners: np.ndarray, image_size: tuple) -> tuple:
        """
        使用形态学方法找到四边形的最大内接矩形

        Args:
            corners: 四个角点坐标
            image_size: 图像尺寸 (width, height)

        Returns:
            最大内接矩形 (left, top, right, bottom)
        """
        w, h = image_size

        # 确保角点在图像范围内
        corners = np.clip(corners, [0, 0], [w-1, h-1])

        # 创建四边形的二值mask
        mask = np.zeros((h, w), dtype=np.uint8)

        # 将角点转换为整数并填充四边形
        corners_int = corners.astype(np.int32)
        cv2.fillPoly(mask, [corners_int], 255)

        # 如果四边形面积太小，返回外接矩形作为fallback
        if np.sum(mask) < 100:  # 面积小于100像素
            self.logger.warning("[MAX_INSCRIBED_RECT] 四边形面积太小，使用外接矩形")
            return self._calculate_bounding_rect_fallback(corners, image_size)

        # 使用距离变换找到最大内接矩形
        inscribed_rect = self._find_largest_rectangle_in_mask(mask)

        if inscribed_rect is None:
            self.logger.warning("[MAX_INSCRIBED_RECT] 无法找到内接矩形，使用外接矩形")
            return self._calculate_bounding_rect_fallback(corners, image_size)

        return inscribed_rect

    def _find_largest_rectangle_in_mask(self, mask: np.ndarray) -> tuple:
        """
        在二值mask中找到最大的矩形区域

        Args:
            mask: 二值mask (255为有效区域)

        Returns:
            最大矩形 (left, top, right, bottom) 或 None
        """
        # 转换为二值图像 (0和1)
        binary_mask = (mask > 0).astype(np.uint8)

        # 使用动态规划算法找最大矩形
        h, w = binary_mask.shape
        max_area = 0
        best_rect = None

        # 对每一行计算直方图
        heights = np.zeros(w, dtype=int)

        for i in range(h):
            # 更新当前行的高度直方图
            for j in range(w):
                if binary_mask[i, j] == 1:
                    heights[j] += 1
                else:
                    heights[j] = 0

            # 在当前直方图中找最大矩形
            rect = self._largest_rectangle_in_histogram(heights, i)
            if rect and rect[4] > max_area:  # rect[4] 是面积
                max_area = rect[4]
                best_rect = rect[:4]  # (left, top, right, bottom)

        return best_rect

    def _largest_rectangle_in_histogram(self, heights: np.ndarray, current_row: int) -> tuple:
        """
        在直方图中找到最大矩形

        Args:
            heights: 高度数组
            current_row: 当前行号

        Returns:
            (left, top, right, bottom, area) 或 None
        """
        stack = []
        max_area = 0
        best_rect = None

        for i in range(len(heights)):
            while stack and heights[i] < heights[stack[-1]]:
                h = heights[stack.pop()]
                w = i if not stack else i - stack[-1] - 1
                area = h * w

                if area > max_area:
                    max_area = area
                    left = int(0 if not stack else stack[-1] + 1)
                    right = int(i)
                    top = int(current_row - h + 1)
                    bottom = int(current_row + 1)
                    best_rect = (left, top, right, bottom, area)

            stack.append(i)

        # 处理栈中剩余的元素
        while stack:
            h = heights[stack.pop()]
            w = len(heights) if not stack else len(heights) - stack[-1] - 1
            area = h * w

            if area > max_area:
                max_area = area
                left = int(0 if not stack else stack[-1] + 1)
                right = int(len(heights))
                top = int(current_row - h + 1)
                bottom = int(current_row + 1)
                best_rect = (left, top, right, bottom, area)

        return best_rect

    def _calculate_bounding_rect_fallback(self, corners: np.ndarray, image_size: tuple) -> tuple:
        """
        计算外接矩形作为fallback

        Args:
            corners: 四个角点
            image_size: 图像尺寸

        Returns:
            外接矩形 (left, top, right, bottom)
        """
        w, h = image_size

        x_coords = corners[:, 0]
        y_coords = corners[:, 1]

        left = int(max(0, int(np.floor(np.min(x_coords)))))
        top = int(max(0, int(np.floor(np.min(y_coords)))))
        right = int(min(w, int(np.ceil(np.max(x_coords)))))
        bottom = int(min(h, int(np.ceil(np.max(y_coords)))))

        # 确保有效性
        left = int(max(0, min(left, w-1)))
        top = int(max(0, min(top, h-1)))
        right = int(max(left+1, min(right, w)))
        bottom = int(max(top+1, min(bottom, h)))

        return (left, top, right, bottom)

    def _calculate_black_edge_regions(self, content_bbox: tuple, image_size: tuple) -> list:
        """
        计算黑边区域

        Args:
            content_bbox: 内容边界框 (left, top, right, bottom)
            image_size: 图像尺寸 (width, height)

        Returns:
            黑边区域列表，每个区域为 (left, top, right, bottom)
        """
        w, h = image_size
        content_left, content_top, content_right, content_bottom = content_bbox

        black_edge_regions = []

        # 上边黑边
        if content_top > 0:
            black_edge_regions.append((0, 0, w, content_top))

        # 下边黑边
        if content_bottom < h:
            black_edge_regions.append((0, content_bottom, w, h))

        # 左边黑边
        if content_left > 0:
            black_edge_regions.append((0, content_top, content_left, content_bottom))

        # 右边黑边
        if content_right < w:
            black_edge_regions.append((content_right, content_top, w, content_bottom))

        # 验证所有区域的有效性
        valid_regions = []
        for region in black_edge_regions:
            left, top, right, bottom = region
            if left < right and top < bottom:
                valid_regions.append(region)
            else:
                self.logger.warning(f"跳过无效的黑边区域: {region}")

        return valid_regions

    def _calculate_table_safe_area_from_transform(self, transformed_corners: np.ndarray, image_size: tuple) -> tuple:
        """
        计算表格安全区域（确保表格内容不被裁剪的最小区域）

        Args:
            transformed_corners: 变换后的四个角点
            image_size: 图像尺寸 (width, height)

        Returns:
            表格安全区域 (left, top, right, bottom)
        """
        w, h = image_size

        # 提取x, y坐标
        x_coords = transformed_corners[:, 0]
        y_coords = transformed_corners[:, 1]

        # 计算表格的实际占用区域，添加小的安全边距
        safety_margin = 2  # 像素安全边距

        left = int(max(0, int(np.floor(np.min(x_coords))) - safety_margin))
        top = int(max(0, int(np.floor(np.min(y_coords))) - safety_margin))
        right = int(min(w, int(np.ceil(np.max(x_coords))) + safety_margin))
        bottom = int(min(h, int(np.ceil(np.max(y_coords))) + safety_margin))

        # 确保安全区域有效
        if left >= right or top >= bottom:
            self.logger.warning(f"无效的表格安全区域: left={left}, top={top}, right={right}, bottom={bottom}")
            # 返回整个图像作为fallback
            return (0, 0, w, h)

        # 再次确保安全区域在图像范围内
        left = max(0, min(left, w-1))
        top = max(0, min(top, h-1))
        right = max(left+1, min(right, w))
        bottom = max(top+1, min(bottom, h))

        return (left, top, right, bottom)

    def _create_black_edge_visualization(self, image: Image.Image, black_edge_info: dict) -> Image.Image:
        """
        创建黑边区域的可视化图像

        Args:
            image: 原始图像
            black_edge_info: 黑边信息

        Returns:
            可视化图像
        """
        from PIL import ImageDraw

        # 创建图像副本用于绘制
        vis_image = image.copy()
        draw = ImageDraw.Draw(vis_image)

        # 绘制黑边区域（红色半透明）
        if black_edge_info['has_black_edges']:
            for region in black_edge_info['black_edge_regions']:
                left, top, right, bottom = region
                # 确保坐标有效
                if left >= right or top >= bottom:
                    continue

                # 绘制红色矩形边框
                draw.rectangle([left, top, right, bottom], outline='red', width=3)

                # 添加半透明填充（通过多次绘制实现）
                for i in range(0, 30, 5):  # 减少迭代次数，避免坐标问题
                    shrink = i // 5
                    inner_left = left + shrink
                    inner_top = top + shrink
                    inner_right = right - shrink
                    inner_bottom = bottom - shrink

                    # 确保内部矩形仍然有效
                    if inner_left < inner_right and inner_top < inner_bottom:
                        draw.rectangle([inner_left, inner_top, inner_right, inner_bottom],
                                     outline='red', width=1)

        # 绘制内容区域边界（蓝色）
        content_bbox = black_edge_info['content_bbox']
        if content_bbox:
            left, top, right, bottom = content_bbox
            # 确保坐标有效
            if left < right and top < bottom:
                draw.rectangle([left, top, right, bottom], outline='blue', width=2)

        # V4.4.2新增：绘制原始最大内接矩形（如果有优化）
        if 'original_inscribed_rect' in black_edge_info and black_edge_info.get('optimization_applied', False):
            original_rect = black_edge_info['original_inscribed_rect']
            if original_rect:
                left, top, right, bottom = original_rect
                if left < right and top < bottom:
                    draw.rectangle([left, top, right, bottom], outline='cyan', width=1)
                    draw.text((left+5, top+5), "Original Max Inscribed", fill='cyan')

        # 绘制表格安全区域（绿色）
        table_safe_area = black_edge_info['table_safe_area']
        if table_safe_area:
            left, top, right, bottom = table_safe_area
            # 确保坐标有效
            if left < right and top < bottom:
                draw.rectangle([left, top, right, bottom], outline='green', width=2)

        # 绘制变换后的角点（黄色圆点）
        transformed_corners = black_edge_info['transformed_corners']
        for corner in transformed_corners:
            x, y = corner
            draw.ellipse([x-5, y-5, x+5, y+5], fill='yellow', outline='orange', width=2)

        # V4.4.2增强：绘制实际边缘异常检测结果
        if 'actual_edge_detection' in black_edge_info:
            actual_edge = black_edge_info['actual_edge_detection']
            edge_width = actual_edge['edge_width_pixels']

            # 绘制检测到的异常边缘（紫色）
            w, h = vis_image.size

            if actual_edge['top_anomaly']['is_anomalous'] and edge_width > 0:
                draw.rectangle([0, 0, w, min(edge_width, h)], outline='purple', width=3)
                draw.text((10, 5), f"TOP: {actual_edge['top_anomaly']['anomaly_score']:.2f}", fill='purple')

            if actual_edge['bottom_anomaly']['is_anomalous'] and edge_width > 0:
                bottom_start = max(0, h - edge_width)
                if bottom_start < h:
                    draw.rectangle([0, bottom_start, w, h], outline='purple', width=3)
                    draw.text((10, max(5, h-20)), f"BOTTOM: {actual_edge['bottom_anomaly']['anomaly_score']:.2f}", fill='purple')

            if actual_edge['left_anomaly']['is_anomalous'] and edge_width > 0:
                draw.rectangle([0, 0, min(edge_width, w), h], outline='purple', width=3)
                draw.text((5, h//2), f"LEFT: {actual_edge['left_anomaly']['anomaly_score']:.2f}", fill='purple')

            if actual_edge['right_anomaly']['is_anomalous'] and edge_width > 0:
                right_start = max(0, w - edge_width)
                if right_start < w:
                    draw.rectangle([right_start, 0, w, h], outline='purple', width=3)
                    draw.text((max(5, w-100), h//2), f"RIGHT: {actual_edge['right_anomaly']['anomaly_score']:.2f}", fill='purple')

            # 在图像顶部显示总体异常评分和算法信息
            draw.text((w//2-100, 5), f"Total Anomaly: {actual_edge['total_anomaly_score']:.3f}",
                     fill='white', stroke_fill='black', stroke_width=1)

            # 显示算法版本信息
            optimization_applied = black_edge_info.get('optimization_applied', False)
            algo_info = "V4.4.2: Max Inscribed Rect" + (" + Table Opt" if optimization_applied else "")
            draw.text((10, h-25), algo_info, fill='white', stroke_fill='black', stroke_width=1)

        return vis_image

    def _detect_actual_edge_anomalies(self, image: Image.Image) -> dict:
        """
        检测图像边缘的实际异常区域（适用于CSS模式）

        在CSS模式下，透视变换后的边缘可能不是纯黑色，
        而是背景图的重复、拉伸或其他异常模式

        Args:
            image: 透视变换后的图像

        Returns:
            实际边缘异常信息
        """
        import numpy as np

        img_array = np.array(image)
        h, w = img_array.shape[:2]

        # 定义边缘检测区域（图像边缘的一定比例）
        edge_ratio = 0.05  # 检测边缘5%的区域
        edge_width = max(5, int(min(w, h) * edge_ratio))

        # 提取四个边缘区域
        top_edge = img_array[:edge_width, :]
        bottom_edge = img_array[-edge_width:, :]
        left_edge = img_array[:, :edge_width]
        right_edge = img_array[:, -edge_width:]

        # 计算图像中心区域作为参考
        center_h_start = h // 4
        center_h_end = 3 * h // 4
        center_w_start = w // 4
        center_w_end = 3 * w // 4
        center_region = img_array[center_h_start:center_h_end, center_w_start:center_w_end]

        # 分析边缘异常
        edge_analysis = {
            'top_anomaly': self._analyze_edge_region(top_edge, center_region, 'top'),
            'bottom_anomaly': self._analyze_edge_region(bottom_edge, center_region, 'bottom'),
            'left_anomaly': self._analyze_edge_region(left_edge, center_region, 'left'),
            'right_anomaly': self._analyze_edge_region(right_edge, center_region, 'right'),
            'edge_width_pixels': edge_width,
            'analysis_method': 'css_mode_edge_detection'
        }

        # 计算总体异常评分
        total_anomaly_score = sum([
            edge_analysis['top_anomaly']['anomaly_score'],
            edge_analysis['bottom_anomaly']['anomaly_score'],
            edge_analysis['left_anomaly']['anomaly_score'],
            edge_analysis['right_anomaly']['anomaly_score']
        ]) / 4

        edge_analysis['total_anomaly_score'] = float(total_anomaly_score)

        # 检查是否有任何单边的异常评分超过阈值
        individual_anomaly_detected = any([
            edge_analysis['top_anomaly']['anomaly_score'] > 0.4,
            edge_analysis['bottom_anomaly']['anomaly_score'] > 0.4,
            edge_analysis['left_anomaly']['anomaly_score'] > 0.4,
            edge_analysis['right_anomaly']['anomaly_score'] > 0.4
        ])

        # 综合判断：总体评分或单边评分超过阈值
        edge_analysis['has_significant_anomalies'] = bool(total_anomaly_score > 0.25 or individual_anomaly_detected)

        self.logger.info(f"[EDGE_DETECTION] 边缘异常检测完成: total_score={total_anomaly_score:.3f}, "
                        f"has_anomalies={edge_analysis['has_significant_anomalies']}")

        return edge_analysis

    def _analyze_edge_region(self, edge_region: np.ndarray, center_region: np.ndarray, edge_name: str) -> dict:
        """
        分析单个边缘区域的异常程度

        Args:
            edge_region: 边缘区域像素
            center_region: 中心区域像素（作为参考）
            edge_name: 边缘名称

        Returns:
            边缘分析结果
        """
        # 计算边缘区域的统计特征
        edge_mean = np.mean(edge_region)
        edge_std = np.std(edge_region)
        edge_var = np.var(edge_region)

        # 计算中心区域的统计特征
        center_mean = np.mean(center_region)
        center_std = np.std(center_region)

        # 计算异常指标
        # 1. 亮度差异
        brightness_diff = abs(edge_mean - center_mean) / 255.0

        # 2. 方差比较（边缘区域通常方差较小，因为是重复或拉伸）
        variance_ratio = edge_var / (center_std ** 2 + 1e-6)

        # 3. 边缘区域内部的一致性（异常边缘通常很一致）
        edge_consistency = 1.0 - (edge_std / 255.0)

        # 综合异常评分
        anomaly_score = (brightness_diff * 0.4 +
                        (1.0 - variance_ratio) * 0.3 +
                        edge_consistency * 0.3)

        anomaly_score = max(0.0, min(1.0, anomaly_score))

        return {
            'edge_name': edge_name,
            'anomaly_score': float(anomaly_score),
            'brightness_diff': float(brightness_diff),
            'variance_ratio': float(variance_ratio),
            'edge_consistency': float(edge_consistency),
            'edge_mean': float(edge_mean),
            'edge_std': float(edge_std),
            'is_anomalous': bool(anomaly_score > 0.4)
        }

    def _apply_background_composition(self,
                                    image: Image.Image,
                                    annotations: Optional[Dict[str, Any]],
                                    background_path: str,
                                    max_scale_factor: float,
                                    background_params=None) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        应用背景图合成

        ⚠️ V5.3废弃警告：此方法已废弃，统一使用CSS背景模式
        后处理背景合成已被移除，所有背景处理都在CSS阶段完成

        Args:
            image: PIL图像对象
            annotations: 标注数据
            background_path: 背景图路径
            max_scale_factor: 最大缩放倍数
            background_params: V5.2修复：背景相关参数，包含margin_control等配置

        Returns:
            (合成后的图像, 更新后的标注)
        """
        # V5.3架构简化：此方法已废弃，直接返回原图像
        self.logger.warning(f"[DEPRECATED] _apply_background_composition方法已废弃")
        self.logger.warning(f"[DEPRECATED] 请使用CSS背景模式获得更好的效果和完整的大表格支持")
        self.logger.warning(f"[DEPRECATED] 返回原图像，不进行后处理背景合成")

        return image, annotations
        # V5.3架构简化：移除所有后处理背景合成代码
        # 直接返回原图像，所有背景处理都在CSS阶段完成
        return image, annotations

    def _make_json_serializable(self, obj):
        """
        将对象转换为JSON可序列化的格式

        Args:
            obj: 要转换的对象

        Returns:
            JSON可序列化的对象
        """
        import numpy as np

        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, bool) or (hasattr(np, 'bool_') and isinstance(obj, np.bool_)):
            return bool(obj)
        elif hasattr(obj, '__dict__'):
            # 对于自定义对象，尝试转换其属性
            return self._make_json_serializable(obj.__dict__)
        else:
            # 对于其他类型，尝试直接返回，如果不能序列化会在json.dump时报错
            return obj

    def _simple_rect_adjustment_for_table(self, inscribed_rect: tuple, table_safe_area: tuple, image_size: tuple) -> tuple:
        """
        简化的矩形调整：直接推移边界以包含表格区域

        Args:
            inscribed_rect: 最大内接矩形
            table_safe_area: 表格安全区域
            image_size: 图像尺寸

        Returns:
            调整后的内容区域
        """
        inscribed_left, inscribed_top, inscribed_right, inscribed_bottom = inscribed_rect
        table_left, table_top, table_right, table_bottom = table_safe_area

        # 检查最大内接矩形是否已经包含表格区域
        if (inscribed_left <= table_left and inscribed_top <= table_top and
            inscribed_right >= table_right and inscribed_bottom >= table_bottom):
            self.logger.debug("[RECT_ADJUST] 最大内接矩形已包含表格区域，无需调整")
            return inscribed_rect

        self.logger.info("[RECT_ADJUST] 最大内接矩形无法完全包含表格，开始简单调整")

        # 简单直接：哪边不够就推哪边
        final_left = min(inscribed_left, table_left)
        final_top = min(inscribed_top, table_top)
        final_right = max(inscribed_right, table_right)
        final_bottom = max(inscribed_bottom, table_bottom)

        # 确保不超出图像边界
        w, h = image_size
        final_left = max(0, final_left)
        final_top = max(0, final_top)
        final_right = min(w, final_right)
        final_bottom = min(h, final_bottom)

        adjusted_rect = (int(final_left), int(final_top), int(final_right), int(final_bottom))
        self.logger.info(f"[RECT_ADJUST] 调整完成: {inscribed_rect} -> {adjusted_rect}")

        return adjusted_rect

    def _apply_content_area_shrink(self, content_bbox: tuple, shrink_ratio: float) -> tuple:
        """
        对内容区域应用缩小比例，更保守地避免黑边

        Args:
            content_bbox: 内容边界框 (left, top, right, bottom)
            shrink_ratio: 缩小比例 (0.0-1.0)

        Returns:
            缩小后的内容区域
        """
        if shrink_ratio <= 0:
            return content_bbox

        left, top, right, bottom = content_bbox
        width = right - left
        height = bottom - top

        # 计算缩小的像素数（四个方向平均分配）
        shrink_w = int(width * shrink_ratio / 2)  # 左右各缩小一半
        shrink_h = int(height * shrink_ratio / 2)  # 上下各缩小一半

        # 应用缩小
        shrinked_left = left + shrink_w
        shrinked_top = top + shrink_h
        shrinked_right = right - shrink_w
        shrinked_bottom = bottom - shrink_h

        # 确保缩小后的区域仍然有效
        if shrinked_left >= shrinked_right or shrinked_top >= shrinked_bottom:
            self.logger.warning(f"[CONTENT_SHRINK] 缩小比例{shrink_ratio}过大，保持原始区域")
            return content_bbox

        shrinked_bbox = (int(shrinked_left), int(shrinked_top), int(shrinked_right), int(shrinked_bottom))

        self.logger.info(f"[CONTENT_SHRINK] 缩小比例: {shrink_ratio}")
        self.logger.info(f"[CONTENT_SHRINK] 原始区域: {content_bbox}")
        self.logger.info(f"[CONTENT_SHRINK] 缩小后区域: {shrinked_bbox}")
        self.logger.info(f"[CONTENT_SHRINK] 缩小像素: 宽度{shrink_w*2}, 高度{shrink_h*2}")

        return shrinked_bbox



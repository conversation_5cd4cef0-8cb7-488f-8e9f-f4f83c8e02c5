"""
降质处理器

V4.4.1更新：内部化8种降质效果实现，移除对doc_degradation模块的依赖。
提供高斯模糊、运动模糊、平均模糊、高斯噪声、全局褪色、局部褪色、
不均匀光照、JPEG压缩、亮度对比度调整、伽马校正等降质效果。
"""

import logging
import numpy as np
from PIL import Image, ImageEnhance
from typing import Optional, Dict, Any, List
import io
import cv2
import random

from ..config import ResolvedPostprocessingParams


class DegradationProcessor:
    """
    降质处理器

    内部化实现8种降质效果，完全移除对doc_degradation模块的依赖。
    支持模糊组内权重选择逻辑和完整的错误处理机制。

    支持的降质效果：
    - blur: 模糊效果（高斯25%、运动50%、平均25%权重选择）
    - noise: 高斯噪声
    - fade_global: 全局褪色
    - fade_local: 局部褪色
    - uneven_lighting: 不均匀光照
    - jpeg: JPEG压缩
    - darker_brighter: 亮度对比度调整
    - gamma_correction: 伽马校正
    """
    
    def __init__(self, seed: int):
        """
        初始化降质处理器

        Args:
            seed: 随机种子，用于确保可复现性
        """
        self.logger = logging.getLogger(__name__)

        self.seed = seed

        # 设置随机种子
        random.seed(seed)
        np.random.seed(seed)

        # 内部配置参数（基于doc_degradation配置优化）
        self.internal_config = {
            # ==================== 模糊类效果参数 ====================

            # 高斯模糊参数
            'gaussian_blur_kernel_size_range': (1, 5),  # 支持可变核大小
            'gaussian_blur_sigma_range': (0.5, 2.0),    # 扩大sigma范围

            # 运动模糊参数
            'motion_blur_kernel_range': (1, 5),         # 扩大核范围
            'motion_blur_angle_range': (0, 180),        # 完整角度范围

            # 平均模糊参数
            'average_blur_kernel_range': (1, 5),        # 与高斯模糊一致

            # 模糊组权重（高斯25%，运动50%，平均25%）
            'blur_weights': [0.25, 0.50, 0.25],

            # ==================== 噪声类效果参数 ====================

            # 高斯噪声参数
            'gaussian_noise_std_range': (2.0, 10.0),     # 扩大噪声强度范围
            'gaussian_noise_mean': 0,                    # 噪声均值

            # ==================== 褪色类效果参数 ====================

            # 全局褪色参数
            'fade_global_kernel_range': (1, 2),         # 扩大膨胀核范围

            # 局部褪色参数
            'fade_local_brightness_range': (0.9, 1.3),  # 扩大亮度调整范围
            'fade_local_saturation_range': (0.9, 1.3),  # 扩大饱和度调整范围
            'fade_local_num_blocks_range': (2, 9),      # 扩大区域数量范围
            'fade_local_block_size_ratio_range': (0.1, 0.3),  # 区域大小比例

            # ==================== 光照类效果参数 ====================

            # 不均匀光照参数
            'uneven_lighting_intensity_range': (0.2, 0.8),     # 光照强度范围
            'uneven_lighting_center_x_range': (0.1, 0.9),      # 光照中心X范围
            'uneven_lighting_center_y_range': (0.1, 0.9),      # 光照中心Y范围
            'uneven_lighting_min_brightness': 0.2,             # 最小亮度限制
            'uneven_lighting_max_brightness': 1.0,             # 最大亮度限制

            # ==================== 压缩类效果参数 ====================

            # JPEG压缩参数
            'jpeg_quality_range': (20, 50),              # 扩大质量范围（更强降质）
            'jpeg_format': '.jpg',                      # 压缩格式

            # ==================== 颜色调整类效果参数 ====================

            # 伽马校正参数
            'gamma_range': (0.5, 3.0),                  # 扩大伽马范围（支持变亮和变暗）

            # 亮度对比度参数
            'darker_bright_contrast_range': (0.5, 2.0), # 扩大对比度范围
            'darker_bright_brightness_range': (0.6, 1.2), # 扩大亮度范围

            # ==================== 算法控制参数 ====================

            # 像素值范围控制
            'pixel_value_min': 0,
            'pixel_value_max': 255,

            # 随机种子控制
            'use_random_seed': True,

            # 性能优化参数
            'max_image_size_for_complex_effects': 2048,  # 复杂效果的最大图像尺寸
        }
    

    
    def apply_degradations(self, 
                          image: Image.Image, 
                          annotations: Optional[Dict[str, Any]], 
                          params: ResolvedPostprocessingParams) -> tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        应用降质效果
        
        Args:
            image: PIL图像对象
            annotations: 标注数据
            params: 解析后的后处理参数
            
        Returns:
            (处理后的PIL图像对象, 处理后的标注数据)
        """

        
        # 获取需要应用的降质效果
        enabled_effects = self._get_enabled_effects(params)
        
        if not enabled_effects:
            self.logger.debug("没有启用的降质效果")
            return image, annotations
        
        self.logger.info(f"开始应用降质效果: {enabled_effects}")
        
        # 转换PIL图像为numpy数组
        image_array = np.array(image)
        
        # 按顺序应用降质效果
        processed_image_array = image_array
        for effect_name in enabled_effects:
            try:
                processed_image_array = self._apply_single_effect(
                    processed_image_array, effect_name
                )
                self.logger.debug(f"降质效果 {effect_name} 应用成功")
                
            except Exception as e:
                self.logger.error(f"降质效果 {effect_name} 应用失败: {e}")
                # 继续处理其他效果，不中断整个流程
                continue
        
        # 转换回PIL图像
        processed_image = Image.fromarray(processed_image_array)
        
        self.logger.info("降质效果应用完成")
        
        # 注意：降质效果通常不会改变图像尺寸，所以标注坐标保持不变
        return processed_image, annotations
    
    def _get_enabled_effects(self, params: ResolvedPostprocessingParams) -> List[str]:
        """
        获取启用的降质效果列表
        
        Args:
            params: 解析后的后处理参数
            
        Returns:
            启用的降质效果名称列表
        """
        enabled_effects = []
        
        # 按照PRD中定义的顺序检查各种降质效果
        effect_checks = [
            ('blur', params.apply_degradation_blur),
            ('noise', params.apply_degradation_noise),
            ('fade_global', params.apply_degradation_fade_global),
            ('fade_local', params.apply_degradation_fade_local),
            ('uneven_lighting', params.apply_degradation_uneven_lighting),
            ('jpeg', params.apply_degradation_jpeg),
            ('darker_brighter', params.apply_degradation_darker_brighter),
            ('gamma_correction', params.apply_degradation_gamma_correction)
        ]
        
        for effect_name, is_enabled in effect_checks:
            if is_enabled:
                enabled_effects.append(effect_name)
        
        return enabled_effects
    
    def _apply_single_effect(self, image_array: np.ndarray, effect_name: str) -> np.ndarray:
        """
        应用单个降质效果

        Args:
            image_array: 图像numpy数组
            effect_name: 降质效果名称

        Returns:
            处理后的图像numpy数组
        """
        # 支持的降质效果列表
        supported_effects = {
            'blur', 'noise', 'fade_global', 'fade_local',
            'uneven_lighting', 'jpeg', 'darker_brighter', 'gamma_correction'
        }

        if effect_name not in supported_effects:
            raise ValueError(f"不支持的降质效果: {effect_name}")

        # 使用内部实现
        if effect_name == 'blur':
            return self._internal_blur_with_weights(image_array)
        elif effect_name == 'noise':
            return self._internal_gaussian_noise(image_array)
        elif effect_name == 'fade_global':
            return self._internal_global_fade(image_array)
        elif effect_name == 'uneven_lighting':
            return self._internal_uneven_lighting(image_array)
        elif effect_name == 'jpeg':
            return self._internal_jpeg_compression(image_array)
        elif effect_name == 'gamma_correction':
            return self._internal_gamma_correction(image_array)
        elif effect_name == 'fade_local':
            return self._internal_local_fade(image_array)
        elif effect_name == 'darker_brighter':
            return self._internal_brightness_contrast(image_array)
        else:
            raise ValueError(f"降质效果 {effect_name} 的内部实现未找到")


    # ==================== 内部降质函数实现 ====================
    #
    # 本节包含8种降质效果的内部实现，完全替代doc_degradation模块
    #
    # 模糊类效果：
    # - _internal_gaussian_blur: 高斯模糊
    # - _internal_motion_blur: 运动模糊
    # - _internal_average_blur: 平均模糊
    # - _internal_blur_with_weights: 权重模糊选择
    #
    # 颜色类效果：
    # - _internal_gaussian_noise: 高斯噪声
    # - _internal_global_fade: 全局褪色
    # - _internal_local_fade: 局部褪色
    # - _internal_uneven_lighting: 不均匀光照
    # - _internal_brightness_contrast: 亮度对比度调整
    # - _internal_gamma_correction: 伽马校正
    #
    # 压缩类效果：
    # - _internal_jpeg_compression: JPEG压缩
    #
    # ================================================================

    def _internal_gaussian_blur(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：高斯模糊效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        kernel_min, kernel_max = self.internal_config['gaussian_blur_kernel_size_range']
        sigma_min, sigma_max = self.internal_config['gaussian_blur_sigma_range']

        # 随机选择核大小（必须为奇数）
        kernel_size = np.random.randint(kernel_min, kernel_max + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 随机选择sigma值
        sigma = np.random.uniform(sigma_min, sigma_max)

        # 应用高斯模糊
        blurred_image = cv2.GaussianBlur(image_array, (kernel_size, kernel_size), sigma)

        self.logger.debug(f"应用高斯模糊: kernel_size={kernel_size}, sigma={sigma:.2f}")
        return blurred_image

    def _internal_gaussian_noise(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：高斯噪声效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        std_min, std_max = self.internal_config['gaussian_noise_std_range']
        mean = self.internal_config['gaussian_noise_mean']
        pixel_min = self.internal_config['pixel_value_min']
        pixel_max = self.internal_config['pixel_value_max']

        # 随机选择噪声标准差
        std = np.random.uniform(std_min, std_max)

        # 生成高斯噪声
        noise = np.random.normal(mean, std, image_array.shape).astype(np.float32)

        # 添加噪声并确保像素值在有效范围内
        noisy_image = image_array.astype(np.float32) + noise
        noisy_image = np.clip(noisy_image, pixel_min, pixel_max).astype(np.uint8)

        self.logger.debug(f"应用高斯噪声: mean={mean}, std={std:.2f}")
        return noisy_image

    def _internal_global_fade(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：全局褪色效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        kernel_min, kernel_max = self.internal_config['fade_global_kernel_range']

        # 随机选择膨胀核大小
        kernel_size = np.random.randint(kernel_min, kernel_max + 1)

        # 随机选择迭代次数
        iterations = 1

        # 创建膨胀核
        kernel = np.ones((kernel_size, kernel_size), np.uint8)

        # 应用膨胀操作实现褪色效果
        faded_image = cv2.dilate(image_array, kernel, iterations=iterations)

        self.logger.debug(f"应用全局褪色: kernel_size={kernel_size}, iterations={iterations}")
        return faded_image

    def _internal_motion_blur(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：运动模糊效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        kernel_min, kernel_max = self.internal_config['motion_blur_kernel_range']
        angle_min, angle_max = self.internal_config['motion_blur_angle_range']

        # 随机选择核大小（必须为奇数）
        kernel_size = np.random.randint(kernel_min, kernel_max + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 随机选择角度
        angle = np.random.uniform(angle_min, angle_max)

        # 创建运动模糊核
        kernel = self._create_motion_blur_kernel(kernel_size, angle)

        # 应用运动模糊
        blurred_image = cv2.filter2D(image_array, -1, kernel)

        self.logger.debug(f"应用运动模糊: kernel_size={kernel_size}, angle={angle:.1f}")
        return blurred_image

    def _create_motion_blur_kernel(self, kernel_size: int, angle: float) -> np.ndarray:
        """
        创建运动模糊核

        Args:
            kernel_size: 核大小
            angle: 运动角度（度）

        Returns:
            运动模糊核
        """
        # 创建空核
        kernel = np.zeros((kernel_size, kernel_size))

        # 计算中心点
        center = kernel_size // 2

        # 将角度转换为弧度
        angle_rad = np.radians(angle)

        # 计算运动方向
        dx = np.cos(angle_rad)
        dy = np.sin(angle_rad)

        # 在核中绘制运动线
        for i in range(kernel_size):
            x = int(center + (i - center) * dx)
            y = int(center + (i - center) * dy)
            if 0 <= x < kernel_size and 0 <= y < kernel_size:
                kernel[y, x] = 1

        # 归一化核
        kernel = kernel / np.sum(kernel) if np.sum(kernel) > 0 else kernel

        return kernel

    def _internal_uneven_lighting(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：不均匀光照效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        intensity_min, intensity_max = self.internal_config['uneven_lighting_intensity_range']
        center_x_min, center_x_max = self.internal_config['uneven_lighting_center_x_range']
        center_y_min, center_y_max = self.internal_config['uneven_lighting_center_y_range']
        min_brightness = self.internal_config['uneven_lighting_min_brightness']
        max_brightness = self.internal_config['uneven_lighting_max_brightness']
        pixel_min = self.internal_config['pixel_value_min']
        pixel_max = self.internal_config['pixel_value_max']

        # 随机选择光照强度
        lighting_intensity = np.random.uniform(intensity_min, intensity_max)

        h, w = image_array.shape[:2]

        # 创建渐变光照图
        x = np.linspace(0, 1, w)
        y = np.linspace(0, 1, h)
        X, Y = np.meshgrid(x, y)

        # 随机选择光照中心
        center_x = np.random.uniform(center_x_min, center_x_max)
        center_y = np.random.uniform(center_y_min, center_y_max)

        # 计算距离并创建光照图
        distance = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
        lighting_map = 1 - lighting_intensity * distance
        lighting_map = np.clip(lighting_map, min_brightness, max_brightness)

        # 应用光照效果
        if len(image_array.shape) == 3:
            lighting_map = np.expand_dims(lighting_map, axis=2)

        lit_image = image_array.astype(np.float32) * lighting_map
        lit_image = np.clip(lit_image, pixel_min, pixel_max).astype(np.uint8)

        self.logger.debug(f"应用不均匀光照: intensity={lighting_intensity:.2f}, center=({center_x:.2f}, {center_y:.2f})")
        return lit_image

    def _internal_jpeg_compression(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：JPEG压缩效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        quality_min, quality_max = self.internal_config['jpeg_quality_range']
        jpeg_format = self.internal_config['jpeg_format']

        # 随机选择JPEG质量
        quality = np.random.randint(quality_min, quality_max + 1)

        # 编码为JPEG格式
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
        _, encoded_img = cv2.imencode(jpeg_format, image_array, encode_param)

        # 解码回图像
        if len(image_array.shape) == 2:
            # 灰度图像
            compressed_image = cv2.imdecode(encoded_img, cv2.IMREAD_GRAYSCALE)
        else:
            # 彩色图像
            compressed_image = cv2.imdecode(encoded_img, cv2.IMREAD_COLOR)
            # OpenCV使用BGR，转换为RGB
            if len(image_array.shape) == 3 and image_array.shape[2] == 3:
                compressed_image = cv2.cvtColor(compressed_image, cv2.COLOR_BGR2RGB)

        # 确保输出尺寸与输入一致
        if compressed_image.shape != image_array.shape:
            self.logger.warning(f"JPEG压缩后尺寸不一致: {compressed_image.shape} vs {image_array.shape}")

        self.logger.debug(f"应用JPEG压缩: quality={quality}")
        return compressed_image

    def _internal_gamma_correction(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：伽马校正效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        gamma_min, gamma_max = self.internal_config['gamma_range']
        pixel_min = self.internal_config['pixel_value_min']
        pixel_max = self.internal_config['pixel_value_max']

        # 随机选择伽马值
        gamma = np.random.uniform(gamma_min, gamma_max)

        # 创建伽马校正查找表
        inv_gamma = 1.0 / gamma
        table = np.array([((i / float(pixel_max)) ** inv_gamma) * pixel_max
                         for i in np.arange(0, 256)]).astype(np.uint8)

        # 应用伽马校正
        gamma_corrected = cv2.LUT(image_array, table)

        # 确保像素值在有效范围内
        gamma_corrected = np.clip(gamma_corrected, pixel_min, pixel_max)

        self.logger.debug(f"应用伽马校正: gamma={gamma:.2f}")
        return gamma_corrected

    def _internal_local_fade(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：局部褪色效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        brightness_min, brightness_max = self.internal_config['fade_local_brightness_range']
        saturation_min, saturation_max = self.internal_config['fade_local_saturation_range']
        num_blocks_min, num_blocks_max = self.internal_config['fade_local_num_blocks_range']
        block_size_min, block_size_max = self.internal_config['fade_local_block_size_ratio_range']

        # 随机选择参数
        brightness_factor = np.random.uniform(brightness_min, brightness_max)
        saturation_factor = np.random.uniform(saturation_min, saturation_max)
        num_blocks = np.random.randint(num_blocks_min, num_blocks_max + 1)

        h, w = image_array.shape[:2]

        # 转换为PIL图像进行处理
        if len(image_array.shape) == 3:
            pil_image = Image.fromarray(image_array)
        else:
            pil_image = Image.fromarray(image_array).convert('RGB')

        # 创建随机区域并应用局部褪色
        for _ in range(num_blocks):
            # 随机选择区域大小比例
            block_size_ratio = np.random.uniform(block_size_min, block_size_max)
            block_w = int(w * block_size_ratio)
            block_h = int(h * block_size_ratio)

            # 随机生成区域位置
            x1 = np.random.randint(0, max(1, w - block_w))
            y1 = np.random.randint(0, max(1, h - block_h))
            x2 = min(x1 + block_w, w)
            y2 = min(y1 + block_h, h)

            # 确保区域有效
            if x2 <= x1 or y2 <= y1:
                continue

            # 提取区域
            region = pil_image.crop((x1, y1, x2, y2))

            # 应用亮度调整
            enhancer = ImageEnhance.Brightness(region)
            region = enhancer.enhance(brightness_factor)

            # 应用饱和度调整
            enhancer = ImageEnhance.Color(region)
            region = enhancer.enhance(saturation_factor)

            # 将处理后的区域粘贴回原图
            pil_image.paste(region, (x1, y1))

        # 转换回numpy数组
        result_array = np.array(pil_image)

        # 确保输出格式与输入一致
        if len(image_array.shape) == 2:
            result_array = cv2.cvtColor(result_array, cv2.COLOR_RGB2GRAY)

        self.logger.debug(f"应用局部褪色: brightness={brightness_factor:.2f}, saturation={saturation_factor:.2f}, blocks={num_blocks}, block_size_ratio={block_size_ratio:.2f}")
        return result_array

    def _internal_brightness_contrast(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：亮度对比度调整效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        contrast_min, contrast_max = self.internal_config['darker_bright_contrast_range']
        brightness_min, brightness_max = self.internal_config['darker_bright_brightness_range']

        # 随机选择参数
        contrast_factor = np.random.uniform(contrast_min, contrast_max)
        brightness_factor = np.random.uniform(brightness_min, brightness_max)

        # 转换为PIL图像进行处理
        if len(image_array.shape) == 3:
            pil_image = Image.fromarray(image_array)
        else:
            pil_image = Image.fromarray(image_array).convert('RGB')

        # 应用对比度调整
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(contrast_factor)

        # 应用亮度调整
        enhancer = ImageEnhance.Brightness(pil_image)
        pil_image = enhancer.enhance(brightness_factor)

        # 转换回numpy数组
        result_array = np.array(pil_image)

        # 确保输出格式与输入一致
        if len(image_array.shape) == 2:
            result_array = cv2.cvtColor(result_array, cv2.COLOR_RGB2GRAY)

        self.logger.debug(f"应用亮度对比度调整: contrast={contrast_factor:.2f}, brightness={brightness_factor:.2f}")
        return result_array

    def _internal_average_blur(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：平均模糊效果

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        kernel_min, kernel_max = self.internal_config['average_blur_kernel_range']

        # 随机选择核大小（必须为奇数）
        kernel_size = np.random.randint(kernel_min, kernel_max + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 创建平均模糊核
        kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)

        # 应用平均模糊
        blurred_image = cv2.filter2D(image_array, -1, kernel)

        self.logger.debug(f"应用平均模糊: kernel_size={kernel_size}")
        return blurred_image

    def _internal_blur_with_weights(self, image_array: np.ndarray) -> np.ndarray:
        """
        内部实现：带权重的模糊效果选择

        根据权重随机选择高斯模糊、运动模糊或平均模糊

        Args:
            image_array: 输入图像numpy数组

        Returns:
            处理后的图像numpy数组
        """
        weights = self.internal_config['blur_weights']
        blur_types = ['gaussian', 'motion', 'average']

        # 根据权重随机选择模糊类型
        chosen_blur = np.random.choice(blur_types, p=weights)

        if chosen_blur == 'gaussian':
            return self._internal_gaussian_blur(image_array)
        elif chosen_blur == 'motion':
            return self._internal_motion_blur(image_array)
        elif chosen_blur == 'average':
            return self._internal_average_blur(image_array)
        else:
            # 默认使用高斯模糊
            return self._internal_gaussian_blur(image_array)

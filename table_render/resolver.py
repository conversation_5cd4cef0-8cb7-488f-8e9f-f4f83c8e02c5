"""
配置解析器

负责将泛化配置转换为具体的、确定性的参数。
这是V3.1架构重构的核心组件，实现了"泛化配置 -> 具体元数据"的工作流。
V3.2版本增强：支持样式继承机制、智能字体选择、颜色对比度保证等新特性。
"""

import logging
import os
import numpy as np
from typing import Any, Dict, List, Union, Optional

from .config import RenderConfig, ResolvedParams, ResolvedPerformanceParams, ProbabilisticRange, ProbabilisticBorderConfig, ProbabilisticOptions, ResolvedSizingParams, PostprocessingConfig
from .utils.font_utils import FontManager
from .utils.color_utils import ColorManager
from .utils.style_utils import StyleInheritanceManager
from .utils.prob_utils import choose_from_list, get_from_range
from .utils.metadata_collector import metadata_collector


class Resolver:
    """
    配置解析器类

    将包含概率性和范围性的配置转换为具体的、确定性的参数。
    每次调用resolve方法都会基于随机种子生成一组具体的参数。
    V3.2版本增强：集成字体管理器、颜色管理器和样式继承管理器。
    """

    def __init__(self):
        """初始化解析器"""
        self.logger = logging.getLogger(__name__)

        # V3.2新增：初始化工具管理器
        self.font_manager = FontManager()
        self.color_manager = ColorManager()
        self.style_inheritance_manager = None  # 在resolve时初始化，因为需要random_state
        
    def resolve(self, config: RenderConfig, seed: int) -> ResolvedParams:
        """
        解析配置为具体参数

        Args:
            config: 原始的渲染配置
            seed: 随机种子，用于确保可复现性

        Returns:
            解析后的具体参数
        """
        # 设置随机状态
        random_state = np.random.RandomState(seed)

        # 保存当前配置，供其他方法使用
        self.current_config = config

        # V3.2：初始化样式继承管理器
        self.style_inheritance_manager = StyleInheritanceManager(random_state)

        self.logger.debug(f"开始解析配置，种子: {seed}")

        # 解析结构参数
        resolved_structure = self._resolve_structure_params(config.structure, random_state)

        # 解析内容参数
        resolved_content = self._resolve_content_params(config.content, random_state)

        # V3.2：增强的样式解析
        resolved_style = self._resolve_style_params(config.style, resolved_structure, random_state)

        # 解析输出参数
        resolved_output = self._resolve_output_params(config.output, random_state)

        # V4.0新增：解析后处理参数
        # V5.1修复：传递config和resolved_structure参数以支持CSS背景参数计算和透视变换动态缩放
        self.logger.info(f"[RESOLVER] 开始解析后处理参数，config类型: {type(config)}")
        resolved_postprocessing = self._resolve_postprocessing_params(config.postprocessing, random_state, config, resolved_structure)
        self.logger.info(f"[RESOLVER] 后处理参数解析完成")

        # V6.0新增：解析性能参数
        resolved_performance = self._resolve_performance_params(config.performance, random_state)

        # 创建解析后的参数对象
        resolved_params = ResolvedParams(
            structure=resolved_structure,
            content=resolved_content,
            style=resolved_style,
            output=resolved_output,
            seed=seed,
            postprocessing=resolved_postprocessing,
            performance=resolved_performance
        )

        # V5.1调试：添加透视变换adaptive_scaling调试信息
        if hasattr(self, '_perspective_adaptive_debug'):
            resolved_params.perspective_adaptive_debug = self._perspective_adaptive_debug
        else:
            resolved_params.perspective_adaptive_debug = {
                'adaptive_scaling_applied': False,
                'reason': '透视变换未被处理或未启用'
            }

        self.logger.debug("配置解析完成")
        return resolved_params

    def _resolve_performance_params(self, performance_config, random_state) -> Optional[Any]:
        """
        解析性能参数

        Args:
            performance_config: 性能配置
            random_state: 随机状态

        Returns:
            解析后的性能参数，如果没有配置则返回None
        """
        if performance_config is None:
            return None

        # 处理max_workers的auto值
        max_workers = performance_config.max_workers
        if isinstance(max_workers, str) and max_workers.lower() == "auto":
            import multiprocessing
            max_workers = multiprocessing.cpu_count()
        elif isinstance(max_workers, str):
            try:
                max_workers = int(max_workers)
            except ValueError:
                max_workers = 1

        return ResolvedPerformanceParams(
            enable_parallel=performance_config.enable_parallel,
            max_workers=max_workers,
            enable_turbo_jpeg=performance_config.enable_turbo_jpeg,
            turbo_jpeg_quality=performance_config.turbo_jpeg_quality,
            turbo_jpeg_format=performance_config.turbo_jpeg_format,
            css_stability_enabled=performance_config.css_stability.enable_validation if performance_config.css_stability else False
        )

    def _resolve_prob_range(self, prob_range: ProbabilisticRange, random_state, category=None) -> Union[int, float]:
        """
        解析一个概率化范围配置，返回一个确定的数值

        Args:
            prob_range: 概率化范围配置
            random_state: 随机状态
            category: 类别名称，用于metadata记录（可选）

        Returns:
            解析后的具体数值
        """
        # 使用概率工具选择一个范围
        chosen_range = choose_from_list(
            prob_range.range_list,
            prob_range.probability_list,
            random_state
        )

        # 从选中的范围中随机选择一个值
        selected_value = get_from_range(chosen_range, random_state)

        # 记录概率化范围选择信息到metadata
        if category:
            try:
                range_index = prob_range.range_list.index(chosen_range)
                range_probability = prob_range.probability_list[range_index]
                metadata_collector.record_range_selection(
                    category=category,
                    selected_value=selected_value,
                    selected_range=chosen_range,
                    range_probability=range_probability,
                    all_ranges=prob_range.range_list,
                    all_probabilities=prob_range.probability_list
                )
            except (ValueError, IndexError):
                pass  # 如果找不到对应的概率，跳过记录

        return selected_value

    def _resolve_value_or_prob_range(self, value, random_state, category=None) -> Union[int, float]:
        """
        解析值或概率化范围：支持固定值、RangeConfig、FloatRangeConfig和ProbabilisticRange

        Args:
            value: 要解析的值
            random_state: 随机状态
            category: 类别名称，用于metadata记录（可选）

        Returns:
            解析后的具体值
        """
        if isinstance(value, ProbabilisticRange):
            return self._resolve_prob_range(value, random_state, category)
        else:
            return self._resolve_value(value, random_state, category)

    def _resolve_prob_options(self, prob_options: ProbabilisticOptions, random_state, category: str = None) -> Any:
        """
        解析概率化选项配置，返回一个确定的选项

        Args:
            prob_options: 概率化选项配置
            random_state: 随机状态
            category: 类别名称，用于metadata记录

        Returns:
            解析后的具体选项
        """
        selected_option = choose_from_list(
            prob_options.option_list,
            prob_options.probability_list,
            random_state
        )

        # 记录选项选择信息到metadata
        if category:
            try:
                option_index = prob_options.option_list.index(selected_option)
                option_probability = prob_options.probability_list[option_index]
                metadata_collector.record_option_selection(
                    category=category,
                    selected_option=selected_option,
                    option_probability=option_probability,
                    all_options=prob_options.option_list,
                    all_probabilities=prob_options.probability_list
                )
            except (ValueError, IndexError):
                pass  # 如果找不到对应的概率，跳过记录

        return selected_option

    def _resolve_choice_or_prob_options(self, value, random_state) -> Any:
        """
        解析选择值或概率化选项：支持单个值、列表和ProbabilisticOptions

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        if isinstance(value, ProbabilisticOptions):
            return self._resolve_prob_options(value, random_state)
        else:
            return self._resolve_choice(value, random_state)

    def _resolve_value_choice_or_prob(self, value, random_state) -> Any:
        """
        解析值、选择或概率化配置：支持固定值、RangeConfig、列表、ProbabilisticRange和ProbabilisticOptions

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        if isinstance(value, (ProbabilisticRange, ProbabilisticOptions)):
            if isinstance(value, ProbabilisticRange):
                return self._resolve_prob_range(value, random_state)
            else:
                return self._resolve_prob_options(value, random_state)
        else:
            # 处理固定值、RangeConfig、FloatRangeConfig或列表
            return self._resolve_value(value, random_state)

    def _resolve_value_choice_or_prob_with_metadata(self, value, random_state, category: str) -> Any:
        """
        解析值、选择或概率化配置，并记录选择信息到metadata

        Args:
            value: 要解析的值
            random_state: 随机状态
            category: 类别名称，用于metadata记录

        Returns:
            解析后的具体值
        """
        if isinstance(value, (ProbabilisticRange, ProbabilisticOptions)):
            if isinstance(value, ProbabilisticRange):
                return self._resolve_prob_range(value, random_state, category)
            else:
                return self._resolve_prob_options(value, random_state, category)
        else:
            # 处理固定值、RangeConfig、FloatRangeConfig或列表
            return self._resolve_value(value, random_state, category)
    
    def _resolve_structure_params(self, structure_config, random_state) -> Any:
        """
        解析结构参数

        Args:
            structure_config: 结构配置
            random_state: 随机状态

        Returns:
            解析后的结构参数
        """
        from .config import ResolvedStructureParams

        # V3.4：支持概率化处理
        return ResolvedStructureParams(
            body_rows=self._resolve_value_or_prob_range(structure_config.body_rows, random_state, "body_rows"),
            cols=self._resolve_value_or_prob_range(structure_config.cols, random_state, "cols"),
            header_rows=self._resolve_value_or_prob_range(structure_config.header_rows, random_state, "header_rows"),
            merge_probability=self._resolve_value_or_prob_range(structure_config.merge_probability, random_state, "merge_probability"),
            max_row_span=self._resolve_value_choice_or_prob_with_metadata(structure_config.max_row_span, random_state, "max_row_span"),
            max_col_span=self._resolve_value_choice_or_prob_with_metadata(structure_config.max_col_span, random_state, "max_col_span"),
            complex_header=structure_config.complex_header
        )
    
    def _resolve_content_params(self, content_config, random_state) -> Any:
        """
        解析内容参数

        Args:
            content_config: 内容配置
            random_state: 随机状态

        Returns:
            解析后的内容参数
        """
        from .config import ResolvedContentParams
        from .utils.csv_utils import select_csv_file

        # 初始化所有参数
        csv_file_path = None
        csv_encoding = None
        csv_mismatch_strategy = None
        csv_dirs = None
        csv_dir_probabilities = None
        sampling_mode = None
        blank_control = None

        if content_config.source_type == 'csv' and content_config.csv_source:
            csv_source = content_config.csv_source

            # 提取基本配置
            csv_encoding = csv_source.encoding
            csv_mismatch_strategy = csv_source.mismatch_strategy
            sampling_mode = csv_source.sampling_mode

            # V5.0新增：空白控制配置
            if csv_source.blank_control:
                blank_control = {
                    'trigger_probability': csv_source.blank_control.trigger_probability,
                    'cell_blank_probability': csv_source.blank_control.cell_blank_probability
                }

            # V5.0新增：处理概率化目录选择
            csv_dirs = csv_source.csv_dirs
            csv_dir_probabilities = csv_source.csv_dir_probabilities

            # 选择具体的CSV文件
            try:
                csv_file_path = select_csv_file(
                    file_path=csv_source.file_path,
                    csv_dirs=csv_dirs,
                    csv_dir_probabilities=csv_dir_probabilities,
                    random_state=random_state
                )
                self.logger.debug(f"选择的CSV文件: {csv_file_path}")
            except Exception as e:
                self.logger.error(f"选择CSV文件失败: {e}")
                raise

        return ResolvedContentParams(
            source_type=content_config.source_type,
            csv_file_path=csv_file_path,
            csv_encoding=csv_encoding,
            csv_mismatch_strategy=csv_mismatch_strategy,
            csv_dirs=csv_dirs,
            csv_dir_probabilities=csv_dir_probabilities,
            sampling_mode=sampling_mode,
            blank_control=blank_control,
            programmatic_types=content_config.programmatic_types
        )
    
    def _resolve_style_params(self, style_config, resolved_structure, random_state) -> Any:
        """
        解析样式参数（V3.4版本）

        Args:
            style_config: 样式配置
            resolved_structure: 解析后的结构参数（用于sizing解析）
            random_state: 随机状态

        Returns:
            解析后的样式参数
        """
        from .config import ResolvedStyleParams, ResolvedBaseStyleParams

        self.logger.debug("使用V3.3样式继承模式")

        # 使用样式继承逻辑
        header_style_dict, body_style_dict = self.style_inheritance_manager.apply_inheritance(
            style_config.common,
            style_config.inheritance
        )

        # V3.3修正：先解析表头得到实际颜色，再解析表体以实现真正的颜色继承
        resolved_header = self._dict_to_resolved_base_style(header_style_dict)

        # 如果表体需要颜色继承，更新表体样式字典中的表头颜色信息
        if body_style_dict.get('_needs_color_inheritance', False):
            body_style_dict['_header_style']['text_color'] = resolved_header.text_color
            body_style_dict['_header_style']['background_color'] = resolved_header.background_color

        resolved_body = self._dict_to_resolved_base_style(body_style_dict)

        # 解析边框模式
        border_mode, border_details = self._resolve_border_mode(style_config.border_mode, random_state)

        # V3.3新增：斑马纹决策和颜色生成
        use_zebra, zebra_colors = self._resolve_zebra_stripes(
            style_config.zebra_stripes, resolved_header, resolved_body, random_state
        )

        # V3.4新增：解析sizing配置
        resolved_sizing = self._resolve_sizing_params(
            style_config.sizing, resolved_structure, random_state
        )

        # V4.0新增：样式冲突检测和日志记录
        self._detect_style_conflicts(style_config, resolved_header, resolved_body)

        return ResolvedStyleParams(
            header=resolved_header,
            body=resolved_body,
            zebra_stripes=use_zebra,
            zebra_colors=zebra_colors,
            sizing=resolved_sizing,
            hierarchical=style_config.hierarchical,
            border_mode=border_mode,
            border_details=border_details,
            merged_cell_center_probability=style_config.common.merged_cell_center_probability,
            randomize_border_color_probability=style_config.common.randomize_border_color_probability,  # V4.5新增
            color_contrast=style_config.common.color_contrast,  # V4.5新增
            overflow_strategy=style_config.overflow_strategy  # V4.0新增
        )

    def _resolve_zebra_stripes(self, zebra_config, resolved_header, resolved_body, random_state):
        """
        解析斑马纹配置并生成颜色

        Args:
            zebra_config: 斑马纹配置（bool或float）
            resolved_header: 解析后的表头样式
            resolved_body: 解析后的表体样式
            random_state: 随机状态

        Returns:
            (是否使用斑马纹, 斑马纹颜色列表) 元组
        """
        # 向后兼容：处理bool类型
        if isinstance(zebra_config, bool):
            if not zebra_config:
                return False, None
            zebra_probability = 1.0  # 如果是True，则100%使用斑马纹
        else:
            zebra_probability = zebra_config

        # 概率决策
        if random_state.random() > zebra_probability:
            return False, None

        # 生成斑马纹颜色：考虑颜色继承
        zebra_colors = self._generate_zebra_colors(resolved_header, resolved_body, random_state)
        return True, zebra_colors

    def _generate_zebra_colors(self, resolved_header, resolved_body, random_state):
        """
        生成斑马纹颜色，考虑颜色继承逻辑

        Args:
            resolved_header: 解析后的表头样式
            resolved_body: 解析后的表体样式
            random_state: 随机状态

        Returns:
            [奇数行背景色, 偶数行背景色] 列表
        """
        # 使用默认的颜色配置
        from .config import ColorContrastConfig
        body_color_contrast = ColorContrastConfig()
        body_randomize_prob = 0.3  # 默认概率

        # 检查表头是否为默认色，用于优化颜色生成
        header_is_default = (resolved_header.text_color == "#000000" and
                           resolved_header.background_color == "#FFFFFF")

        # 如果表头是默认色，强制生成随机颜色避免冲突
        effective_randomize_prob = 1.0 if header_is_default else body_randomize_prob

        # 生成两种背景色用于斑马纹
        color1_text, color1_bg = self.color_manager.get_color_pair(
            effective_randomize_prob, body_color_contrast, random_state
        )
        color2_text, color2_bg = self.color_manager.get_color_pair(
            effective_randomize_prob, body_color_contrast, random_state
        )

        # 返回两种背景色（文本色在斑马纹中统一使用表体的文本色）
        return [color1_bg, color2_bg]

    def _resolve_base_style_params(self, base_style_config, random_state) -> Any:
        """
        解析基础样式参数

        Args:
            base_style_config: 基础样式配置
            random_state: 随机状态

        Returns:
            解析后的基础样式参数
        """
        from .config import ResolvedBaseStyleParams, ResolvedFontParams

        # V3.3：增强的字体解析，支持概率化目录选择
        # V5.3：调整字体选择优先级 - 优先使用font_dirs中的实际字体文件
        selected_font_dir = self.font_manager.get_weighted_random_directory(
            base_style_config.font.font_dirs,
            base_style_config.font.font_dir_probabilities,
            random_state
        )

        # 字体目录选择信息将在确定具体字体文件后记录

        # 扫描选中目录的可用字体
        available_fonts = self.font_manager.scan_font_directories([selected_font_dir])

        # 新的字体选择逻辑：优先使用font_dirs中的实际字体文件
        # 准备默认字体族候选列表作为回退选项
        font_candidates = self._resolve_choice_with_metadata(base_style_config.font.default_family, random_state, "font_family")
        if isinstance(font_candidates, str):
            font_candidates = [font_candidates]

        # 使用新的字体选择方法：优先从可用字体文件中选择
        selected_font = self.font_manager.select_font_from_available(
            available_fonts,
            font_candidates,
            "中文English123",  # 默认字符集测试
            random_state
        )

        # 记录具体选择的字体文件
        selected_font_file = ""
        if selected_font and available_fonts:
            # 尝试找到具体的字体文件路径
            for font_file in available_fonts:
                font_file_name = os.path.splitext(os.path.basename(font_file))[0]
                if selected_font.lower() == font_file_name.lower() or selected_font.lower() in font_file.lower():
                    selected_font_file = font_file
                    break

            # 如果没找到精确匹配，但有可用字体，使用第一个作为fallback
            if not selected_font_file and available_fonts:
                selected_font_file = available_fonts[0]

            # 记录字体目录选择概率
            selected_dir_probability = 0.0
            if base_style_config.font.font_dir_probabilities and len(base_style_config.font.font_dirs) > 0:
                try:
                    dir_index = base_style_config.font.font_dirs.index(selected_font_dir)
                    selected_dir_probability = base_style_config.font.font_dir_probabilities[dir_index]
                except (ValueError, IndexError):
                    # 如果找不到对应的概率，使用平均概率
                    selected_dir_probability = 1.0 / len(base_style_config.font.font_dirs)

            # 使用正确的方法记录字体文件选择信息
            metadata_collector.record_file_selection(
                category="font",
                selected_file=selected_font_file,
                selected_directory=selected_font_dir,
                directory_probability=selected_dir_probability,
                all_directories=base_style_config.font.font_dirs,
                all_probabilities=base_style_config.font.font_dir_probabilities
            )

        # 获取回退字体
        fallback_font = getattr(base_style_config.font, 'fallback_font', 'Microsoft YaHei')

        resolved_font = ResolvedFontParams(
            font_dirs=[selected_font_dir],
            default_family=selected_font,
            default_size=self._resolve_value_or_prob_range(base_style_config.font.default_size, random_state, "font_size"),
            bold_probability=self._resolve_value(base_style_config.font.bold_probability, random_state, "font_bold_probability"),
            italic_probability=self._resolve_value(base_style_config.font.italic_probability, random_state, "font_italic_probability"),
            fallback_font=fallback_font
        )

        # V3.3：概率化颜色生成
        color_probability = getattr(base_style_config, 'randomize_color_probability', 0.3)
        color_contrast_config = getattr(base_style_config, 'color_contrast', None)
        text_color, background_color = self.color_manager.get_color_pair(
            color_probability, color_contrast_config, random_state
        )

        return ResolvedBaseStyleParams(
            font=resolved_font,
            text_color=text_color,
            background_color=background_color,
            horizontal_align=self._resolve_choice_with_metadata(base_style_config.horizontal_align, random_state, "horizontal_align"),
            vertical_align=self._resolve_choice_with_metadata(base_style_config.vertical_align, random_state, "vertical_align"),
            padding=self._resolve_value_or_prob_range(base_style_config.padding, random_state, "padding")
        )
    
    def _resolve_output_params(self, output_config, random_state) -> Any:
        """
        解析输出参数

        Args:
            output_config: 输出配置
            random_state: 随机状态

        Returns:
            解析后的输出参数
        """
        # 在第一步中，直接复制配置
        from .config import ResolvedOutputParams

        return ResolvedOutputParams(
            output_dir=output_config.output_dir,
            label_suffix=output_config.label_suffix
        )

    def _resolve_value(self, value, random_state, category=None):
        """
        解析单个值：如果是RangeConfig或FloatRangeConfig则从范围中选择，否则返回原值

        Args:
            value: 要解析的值
            random_state: 随机状态
            category: 类别名称，用于metadata记录（可选）

        Returns:
            解析后的具体值
        """
        from .config import RangeConfig, FloatRangeConfig

        if isinstance(value, RangeConfig):
            # 整数范围
            selected_value = random_state.randint(value.min, value.max + 1)
            # 记录范围选择信息到metadata
            if category:
                metadata_collector.record_range_selection(
                    category=category,
                    selected_value=selected_value,
                    selected_range=[value.min, value.max],
                    range_probability=1.0  # 单一范围，概率为1.0
                )
            return selected_value
        elif isinstance(value, FloatRangeConfig):
            # 浮点数范围
            selected_value = random_state.uniform(value.min, value.max)
            # 记录范围选择信息到metadata
            if category:
                metadata_collector.record_range_selection(
                    category=category,
                    selected_value=selected_value,
                    selected_range=[value.min, value.max],
                    range_probability=1.0  # 单一范围，概率为1.0
                )
            return selected_value
        else:
            return value

    def _resolve_choice(self, value, random_state):
        """
        解析选择值：如果是列表则随机选择一个，否则返回原值

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        if isinstance(value, list) and len(value) > 0:
            return random_state.choice(value)
        else:
            return value

    def _resolve_choice_with_metadata(self, value, random_state, category: str):
        """
        解析选择值并记录选择信息到metadata

        Args:
            value: 要解析的值
            random_state: 随机状态
            category: 类别名称，用于metadata记录

        Returns:
            解析后的具体值
        """
        if isinstance(value, list) and len(value) > 0:
            selected_value = random_state.choice(value)
            # 记录选择信息到metadata（假设等概率选择）
            option_probability = 1.0 / len(value)
            metadata_collector.record_option_selection(
                category=category,
                selected_option=selected_value,
                option_probability=option_probability,
                all_options=value,
                all_probabilities=[option_probability] * len(value)
            )
            return selected_value
        else:
            return value

    def _dict_to_resolved_base_style(self, style_dict: Dict[str, Any]) -> Any:
        """
        将样式字典转换为ResolvedBaseStyleParams对象

        Args:
            style_dict: 样式字典

        Returns:
            ResolvedBaseStyleParams对象
        """
        from .config import ResolvedBaseStyleParams, ResolvedFontParams

        resolved_font = ResolvedFontParams(
            font_dirs=style_dict['font']['font_dirs'],
            default_family=style_dict['font']['default_family'],
            default_size=style_dict['font']['default_size'],
            bold_probability=style_dict['font']['bold_probability'],
            italic_probability=style_dict['font']['italic_probability'],
            fallback_font=style_dict['font']['fallback_font']
        )

        # V3.3：使用概率化颜色生成或颜色继承
        if style_dict.get('_needs_color_inheritance', False):
            # 表体颜色：使用继承逻辑
            header_style = style_dict['_header_style']
            text_color, background_color = self.color_manager.get_inherited_color_pair(
                header_style.get('text_color', '#000000'),
                header_style.get('background_color', '#FFFFFF'),
                style_dict['_text_color_change_probability'],
                style_dict['_background_color_change_probability'],
                style_dict.get('randomize_color_probability', 0.3),
                style_dict.get('color_contrast', None),
                self.style_inheritance_manager.random_state
            )
        else:
            # 表头颜色：使用标准逻辑
            color_probability = style_dict.get('randomize_color_probability', 0.3)
            color_contrast_config = style_dict.get('color_contrast', None)
            text_color, background_color = self.color_manager.get_color_pair(
                color_probability, color_contrast_config, self.style_inheritance_manager.random_state
            )

        # 注意：不在这里重新记录样式选择信息，避免覆盖原始的range和probability信息
        # 原始的选择信息已经在_resolve_base_style_params和相关方法中正确记录了
        # 这里只记录样式继承相关的变化信息

        # 记录样式继承应用情况
        inheritance_changes = {
            "inheritance_font_family_changed": False,
            "inheritance_font_size_changed": False,
            "inheritance_alignment_changed": False,
            "inheritance_padding_changed": False,
            "inheritance_text_color_changed": False,
            "inheritance_background_color_changed": False
        }

        # 检查是否有样式继承变化（这里可以根据实际的继承逻辑来判断）
        # 暂时设置为False，实际应该根据继承前后的值比较来确定
        metadata_collector.record_multiple_values(inheritance_changes)

        return ResolvedBaseStyleParams(
            font=resolved_font,
            text_color=text_color,
            background_color=background_color,
            horizontal_align=style_dict['horizontal_align'],
            vertical_align=style_dict['vertical_align'],
            padding=style_dict['padding']
        )

    def _resolve_border_mode(self, border_mode_config, random_state) -> tuple:
        """
        解析边框模式配置

        Args:
            border_mode_config: 边框模式配置（固定模式或概率化模式）
            random_state: 随机状态

        Returns:
            (边框模式, 边框详细配置) 元组
        """
        # V3.4：支持概率化边框配置
        if isinstance(border_mode_config, ProbabilisticBorderConfig):
            # 概率化边框配置
            border_options = [opt.config for opt in border_mode_config.mode_options]
            probabilities = [opt.probability for opt in border_mode_config.mode_options]
            chosen_border_config = choose_from_list(border_options, probabilities, random_state)

            # 解析选中的边框配置
            mode = chosen_border_config.get('mode', 'full')
            details = None

            if mode == 'semi' and 'semi_config' in chosen_border_config:
                semi_config = chosen_border_config['semi_config']
                details = {
                    'row_line_probability': semi_config.get('row_line_probability', 0.5),
                    'col_line_probability': semi_config.get('col_line_probability', 0.5),
                    'outer_frame': semi_config.get('outer_frame', True),
                    'header_separator': semi_config.get('header_separator', True)
                }

            return mode, details
        else:
            # 传统的固定边框配置
            mode = border_mode_config.mode
            details = None

            if mode == 'semi' and border_mode_config.semi_config:
                details = {
                    'row_line_probability': border_mode_config.semi_config.row_line_probability,
                    'col_line_probability': border_mode_config.semi_config.col_line_probability,
                    'outer_frame': border_mode_config.semi_config.outer_frame,
                    'header_separator': border_mode_config.semi_config.header_separator
                }

            return mode, details

    def _resolve_sizing_params(self, sizing_config, resolved_structure, random_state) -> ResolvedSizingParams:
        """
        解析sizing配置为具体的行列尺寸

        Args:
            sizing_config: sizing配置
            resolved_structure: 解析后的结构参数（用于获取行列数量）
            random_state: 随机状态

        Returns:
            解析后的sizing参数
        """
        # 计算总行数和列数
        total_rows = resolved_structure.header_rows + resolved_structure.body_rows
        total_cols = resolved_structure.cols

        # 解析默认值
        default_row_height = self._resolve_value(sizing_config.default_row_height, random_state)
        default_col_width = self._resolve_value(sizing_config.default_col_width, random_state)

        # 初始化结果字典
        row_heights = {}
        col_widths = {}

        # 处理行配置
        if sizing_config.row_configs:
            occupied_rows = set()  # 记录已被占用的行

            for config_item in sizing_config.row_configs:
                # 判断配置是否启用 - 修复：确保1.0概率时一定触发
                if random_state.random() <= config_item.probability:
                    self.logger.info(f"启用行配置 '{config_item.name}'")

                    if config_item.type == "specific":
                        # 指定特定行
                        target_rows = self._validate_indices(config_item.target_rows, total_rows, "行")
                        for row_idx in target_rows:
                            if row_idx not in occupied_rows:
                                height = self._get_random_value_from_range(config_item.height_range, random_state)
                                row_heights[row_idx] = height
                                occupied_rows.add(row_idx)
                                self.logger.info(f"应用行配置 '{config_item.name}' 到第{row_idx}行，高度设为{height}px")

                    elif config_item.type == "probabilistic":
                        # 概率触发
                        for row_idx in range(total_rows):
                            if row_idx not in occupied_rows and random_state.random() <= config_item.per_row_probability:
                                height = self._get_random_value_from_range(config_item.height_range, random_state)
                                row_heights[row_idx] = height
                                occupied_rows.add(row_idx)
                                self.logger.info(f"应用行配置 '{config_item.name}' 到第{row_idx}行，高度设为{height}px")

        # 处理列配置
        if sizing_config.col_configs:
            occupied_cols = set()  # 记录已被占用的列

            for config_item in sizing_config.col_configs:
                # 判断配置是否启用 - 修复：确保1.0概率时一定触发
                if random_state.random() <= config_item.probability:
                    self.logger.info(f"启用列配置 '{config_item.name}'")

                    if config_item.type == "specific":
                        # 指定特定列
                        target_cols = self._validate_indices(config_item.target_cols, total_cols, "列")
                        for col_idx in target_cols:
                            if col_idx not in occupied_cols:
                                width = self._get_random_value_from_range(config_item.width_range, random_state)
                                col_widths[col_idx] = width
                                occupied_cols.add(col_idx)
                                self.logger.info(f"应用列配置 '{config_item.name}' 到第{col_idx}列，宽度设为{width}px")

                    elif config_item.type == "probabilistic":
                        # 概率触发
                        for col_idx in range(total_cols):
                            if col_idx not in occupied_cols and random_state.random() <= config_item.per_col_probability:
                                width = self._get_random_value_from_range(config_item.width_range, random_state)
                                col_widths[col_idx] = width
                                occupied_cols.add(col_idx)
                                self.logger.info(f"应用列配置 '{config_item.name}' 到第{col_idx}列，宽度设为{width}px")

        return ResolvedSizingParams(
            row_heights=row_heights,
            col_widths=col_widths,
            default_row_height=default_row_height,
            default_col_width=default_col_width
        )

    def _validate_indices(self, indices, max_count, type_name):
        """
        验证索引列表，过滤无效索引

        Args:
            indices: 索引列表
            max_count: 最大索引数量
            type_name: 类型名称（用于日志）

        Returns:
            有效的索引列表
        """
        if not indices:
            return []

        valid_indices = []
        for idx in indices:
            # 处理负数索引
            if idx < 0:
                idx = max_count + idx

            if 0 <= idx < max_count:
                valid_indices.append(idx)
            else:
                self.logger.warning(f"配置中指定的{type_name}索引{idx}超出范围[0, {max_count-1}]，已忽略")

        return valid_indices

    def _get_random_value_from_range(self, value_range, random_state):
        """
        从范围中获取随机值

        Args:
            value_range: 值范围 [min, max]
            random_state: 随机状态

        Returns:
            随机值
        """
        if not value_range or len(value_range) != 2:
            raise ValueError("值范围必须是包含两个元素的列表 [min, max]")

        min_val, max_val = value_range
        if min_val == max_val:
            return min_val
        else:
            return random_state.randint(min_val, max_val + 1)

    def _resolve_postprocessing_params(self, postprocessing_config, random_state, config, resolved_structure=None):
        """
        解析图像后处理参数

        Args:
            postprocessing_config: 后处理配置，可能为None
            random_state: 随机状态
            config: 完整配置对象（V5.1新增，用于CSS背景参数计算）
            resolved_structure: 已解析的结构参数（V5.1新增，用于透视变换动态缩放）

        Returns:
            解析后的后处理参数，如果配置为None则返回None
        """
        if postprocessing_config is None:
            return None

        from .config import ResolvedPostprocessingParams



        # 解析透视变换效果
        apply_perspective = False
        perspective_offset_ratio = None
        content_area_shrink_ratio = 0.1  # 默认值
        perspective_probability = 0.0

        if postprocessing_config.perspective is not None:
            # 获取内容区域缩小比例和概率
            content_area_shrink_ratio = postprocessing_config.perspective.content_area_shrink_ratio
            perspective_probability = postprocessing_config.perspective.probability

            if random_state.random() < perspective_probability:
                apply_perspective = True
                # 选择透视变换强度
                perspective_offset_ratio = self._select_perspective_offset_ratio(
                    postprocessing_config.perspective, random_state, config, resolved_structure
                )

        # 记录透视变换选择信息到metadata
        metadata_collector.record_multiple_values({
            "apply_perspective": apply_perspective,
            "perspective_probability": perspective_probability,
            "perspective_offset_ratio": perspective_offset_ratio,
            "content_area_shrink_ratio": content_area_shrink_ratio
        })

        # V5.3架构简化：解析背景图合成效果（统一使用CSS模式）
        apply_background = False
        background_image_path = None
        max_scale_factor = None
        css_background_width = None
        css_background_height = None
        css_table_left = None
        css_table_top = None
        css_crop_width = None
        css_crop_height = None

        # V5.3配置验证：确保背景配置的一致性
        if postprocessing_config.background is not None:
            self.logger.info(f"[UNIFIED_BACKGROUND] 检测到背景配置，将统一使用CSS背景模式")
            if hasattr(postprocessing_config.background, 'background_dirs') and postprocessing_config.background.background_dirs:
                self.logger.info(f"[UNIFIED_BACKGROUND] 背景目录数量: {len(postprocessing_config.background.background_dirs)}")
            else:
                self.logger.warning(f"[UNIFIED_BACKGROUND] 警告：背景配置存在但未指定background_dirs")
        css_bg_offset_x = None
        css_bg_offset_y = None

        if postprocessing_config.background is not None:
            apply_background = True
            max_scale_factor = postprocessing_config.background.max_scale_factor

            self.logger.info(f"开始选择背景图，目录: {postprocessing_config.background.background_dirs}")

            # 选择背景图路径
            try:
                background_image_path = self._select_background_image(
                    postprocessing_config.background, random_state
                )
                self.logger.info(f"成功选择背景图: {background_image_path}")
            except Exception as e:
                self.logger.error(f"选择背景图失败: {e}")
                # 不要让背景图选择失败影响整个流程
                apply_background = False
                background_image_path = None

            # 计算CSS背景图参数
            self.logger.info(f"[CSS_DEBUG] 开始计算CSS参数，背景图路径: {background_image_path}")
            self.logger.info(f"[CSS_DEBUG] 准备调用_calculate_css_background_params")
            self.logger.info(f"[CSS_DEBUG] 参数检查 - config类型: {type(config)}")
            self.logger.info(f"[CSS_DEBUG] 参数检查 - config.structure存在: {hasattr(config, 'structure') if config else 'config为None'}")
            try:
                # V5.1修复：传递config参数以支持大表格检测
                css_params = self._calculate_css_background_params(
                    background_image_path, postprocessing_config.background, random_state, config
                )
                css_background_width = css_params['background_width']
                css_background_height = css_params['background_height']
                css_table_left = css_params['table_left']
                css_table_top = css_params['table_top']
                css_crop_width = css_params['crop_width']
                css_crop_height = css_params['crop_height']
                css_bg_offset_x = css_params['bg_offset_x']
                css_bg_offset_y = css_params['bg_offset_y']
                self.logger.info(f"[CSS_DEBUG] CSS参数计算成功: {css_params}")
            except Exception as e:
                self.logger.error(f"[CSS_DEBUG] CSS参数计算失败: {e}")
                # 计算失败时设置默认值，但保持CSS模式
                css_background_width = 1200
                css_background_height = 800
                css_table_left = 200
                css_table_top = 150
                css_crop_width = 1000
                css_crop_height = 600
                css_bg_offset_x = 50   # 使用更保守的默认偏移
                css_bg_offset_y = 50   # 使用更保守的默认偏移
                self.logger.info(f"[CSS_DEBUG] 使用默认CSS参数，背景图路径保持: {background_image_path}")

        # V4.2新增：获取边距控制配置
        margin_control_config = None
        if apply_background and postprocessing_config.background and hasattr(postprocessing_config.background, 'margin_control'):
            margin_control_config = postprocessing_config.background.margin_control
            if margin_control_config:
                self.logger.info(f"[MARGIN_CONTROL] 边距控制配置已加载: {len(margin_control_config.range_list) if margin_control_config.range_list else 0} 个选项")

        # V4.3新增：解析表格透明度配置
        enable_transparency = False
        default_color_transparency = 0.1
        meaningful_color_transparency = 0.7
        if postprocessing_config.table_blending is not None:
            enable_transparency = postprocessing_config.table_blending.enable_transparency
            default_color_transparency = postprocessing_config.table_blending.default_color_transparency
            meaningful_color_transparency = postprocessing_config.table_blending.meaningful_color_transparency
            if enable_transparency:
                self.logger.info(f"[TRANSPARENCY] 表格透明度已启用，默认色透明度: {default_color_transparency}, 有意义颜色透明度: {meaningful_color_transparency}")

        # 记录transparency配置到metadata
        metadata_collector.record_multiple_values({
            "table_blending_enabled": enable_transparency,
            "default_color_transparency": default_color_transparency,
            "meaningful_color_transparency": meaningful_color_transparency
        })

        # V4.5新增：解析降质效果配置
        apply_degradation_blur = False
        apply_degradation_noise = False
        apply_degradation_fade_global = False
        apply_degradation_fade_local = False
        apply_degradation_uneven_lighting = False
        apply_degradation_jpeg = False
        apply_degradation_darker_brighter = False
        apply_degradation_gamma_correction = False

        # 解析各种降质效果
        if postprocessing_config.degradation_blur is not None:
            if random_state.random() < postprocessing_config.degradation_blur.probability:
                apply_degradation_blur = True
                self.logger.info(f"[DEGRADATION] 降质模糊效果已启用")

        if postprocessing_config.degradation_noise is not None:
            if random_state.random() < postprocessing_config.degradation_noise.probability:
                apply_degradation_noise = True
                self.logger.info(f"[DEGRADATION] 降质噪声效果已启用")

        if postprocessing_config.degradation_fade_global is not None:
            if random_state.random() < postprocessing_config.degradation_fade_global.probability:
                apply_degradation_fade_global = True
                self.logger.info(f"[DEGRADATION] 全局褪色效果已启用")

        if postprocessing_config.degradation_fade_local is not None:
            if random_state.random() < postprocessing_config.degradation_fade_local.probability:
                apply_degradation_fade_local = True
                self.logger.info(f"[DEGRADATION] 局部褪色效果已启用")

        if postprocessing_config.degradation_uneven_lighting is not None:
            if random_state.random() < postprocessing_config.degradation_uneven_lighting.probability:
                apply_degradation_uneven_lighting = True
                self.logger.info(f"[DEGRADATION] 不均匀光照效果已启用")

        if postprocessing_config.degradation_jpeg is not None:
            if random_state.random() < postprocessing_config.degradation_jpeg.probability:
                apply_degradation_jpeg = True
                self.logger.info(f"[DEGRADATION] JPEG压缩效果已启用")

        if postprocessing_config.degradation_darker_brighter is not None:
            if random_state.random() < postprocessing_config.degradation_darker_brighter.probability:
                apply_degradation_darker_brighter = True
                self.logger.info(f"[DEGRADATION] 亮度/对比度调整效果已启用")

        if postprocessing_config.degradation_gamma_correction is not None:
            if random_state.random() < postprocessing_config.degradation_gamma_correction.probability:
                apply_degradation_gamma_correction = True
                self.logger.info(f"[DEGRADATION] 伽马校正效果已启用")

        # 记录降质效果选择信息到metadata
        degradation_effects_applied = []
        degradation_effects_config = {}

        if apply_degradation_blur:
            degradation_effects_applied.append("blur")
            degradation_effects_config["blur_probability"] = postprocessing_config.degradation_blur.probability

        if apply_degradation_noise:
            degradation_effects_applied.append("noise")
            degradation_effects_config["noise_probability"] = postprocessing_config.degradation_noise.probability

        if apply_degradation_fade_global:
            degradation_effects_applied.append("fade_global")
            degradation_effects_config["fade_global_probability"] = postprocessing_config.degradation_fade_global.probability

        if apply_degradation_fade_local:
            degradation_effects_applied.append("fade_local")
            degradation_effects_config["fade_local_probability"] = postprocessing_config.degradation_fade_local.probability

        if apply_degradation_uneven_lighting:
            degradation_effects_applied.append("uneven_lighting")
            degradation_effects_config["uneven_lighting_probability"] = postprocessing_config.degradation_uneven_lighting.probability

        if apply_degradation_jpeg:
            degradation_effects_applied.append("jpeg")
            degradation_effects_config["jpeg_probability"] = postprocessing_config.degradation_jpeg.probability

        if apply_degradation_darker_brighter:
            degradation_effects_applied.append("darker_brighter")
            degradation_effects_config["darker_brighter_probability"] = postprocessing_config.degradation_darker_brighter.probability

        if apply_degradation_gamma_correction:
            degradation_effects_applied.append("gamma_correction")
            degradation_effects_config["gamma_correction_probability"] = postprocessing_config.degradation_gamma_correction.probability

        # 记录降质效果到metadata
        # metadata_collector已经在上面导入了，这里直接使用
        metadata_collector.record_multiple_values({
            "degradation_effects_applied": degradation_effects_applied,
            "degradation_effects_config": degradation_effects_config,
            "apply_degradation_blur": apply_degradation_blur,
            "apply_degradation_noise": apply_degradation_noise,
            "apply_degradation_fade_global": apply_degradation_fade_global,
            "apply_degradation_fade_local": apply_degradation_fade_local,
            "apply_degradation_uneven_lighting": apply_degradation_uneven_lighting,
            "apply_degradation_jpeg": apply_degradation_jpeg,
            "apply_degradation_darker_brighter": apply_degradation_darker_brighter,
            "apply_degradation_gamma_correction": apply_degradation_gamma_correction
        })

        # V5.3架构简化：统一使用CSS背景模式，废弃后处理背景合成
        # 当启用背景时，强制使用CSS模式以获得最佳质量和5行5列扩展支持
        if apply_background and background_image_path is not None:
            # 强制启用CSS背景模式
            css_background_applied = True
            self.logger.info(f"[UNIFIED_BACKGROUND] 强制启用CSS背景模式以获得最佳质量")
            self.logger.info(f"[UNIFIED_BACKGROUND] 背景图: {os.path.basename(background_image_path)}")

            # 确保CSS参数已正确计算
            if css_background_width is None or css_background_height is None:
                self.logger.warning(f"[UNIFIED_BACKGROUND] CSS参数未正确计算，重新计算")
                # 重新计算CSS背景参数
                css_params = self._calculate_css_background_params(
                    background_image_path, background_config, random_state, config
                )
                css_background_width = css_params['css_background_width']
                css_background_height = css_params['css_background_height']
                css_table_left = css_params['css_table_left']
                css_table_top = css_params['css_table_top']
                css_crop_width = css_params['css_crop_width']
                css_crop_height = css_params['css_crop_height']
                css_bg_offset_x = css_params['css_bg_offset_x']
                css_bg_offset_y = css_params['css_bg_offset_y']
        else:
            css_background_applied = False

        # 创建最终的后处理参数
        resolved_params = ResolvedPostprocessingParams(
            apply_perspective=apply_perspective,
            perspective_offset_ratio=perspective_offset_ratio,
            content_area_shrink_ratio=content_area_shrink_ratio,
            apply_background=apply_background,
            background_image_path=background_image_path,
            max_scale_factor=max_scale_factor,
            css_background_width=css_background_width,
            css_background_height=css_background_height,
            css_table_left=css_table_left,
            css_table_top=css_table_top,
            css_crop_width=css_crop_width,
            css_crop_height=css_crop_height,
            css_bg_offset_x=css_bg_offset_x,
            css_bg_offset_y=css_bg_offset_y,
            margin_control=margin_control_config,
            # V5.2新增：背景处理模式标识
            css_background_applied=css_background_applied,
            # V4.3新增：透明度参数
            enable_transparency=enable_transparency,
            default_color_transparency=default_color_transparency,
            meaningful_color_transparency=meaningful_color_transparency,
            # V4.5新增：降质效果参数
            apply_degradation_blur=apply_degradation_blur,
            apply_degradation_noise=apply_degradation_noise,
            apply_degradation_fade_global=apply_degradation_fade_global,
            apply_degradation_fade_local=apply_degradation_fade_local,
            apply_degradation_uneven_lighting=apply_degradation_uneven_lighting,
            apply_degradation_jpeg=apply_degradation_jpeg,
            apply_degradation_darker_brighter=apply_degradation_darker_brighter,
            apply_degradation_gamma_correction=apply_degradation_gamma_correction
        )

        # 记录关键的CSS渲染参数
        if apply_background:
            self.logger.info(f"[UNIFIED_BACKGROUND] CSS渲染模式参数:")
            self.logger.info(f"[UNIFIED_BACKGROUND]   背景图: {os.path.basename(background_image_path) if background_image_path else 'None'}")
            self.logger.info(f"[UNIFIED_BACKGROUND]   输出尺寸: {css_crop_width}x{css_crop_height}")
            self.logger.info(f"[UNIFIED_BACKGROUND]   表格位置: ({css_table_left}, {css_table_top})")
            self.logger.info(f"[UNIFIED_BACKGROUND]   CSS背景已应用: {css_background_applied}")
            self.logger.info(f"[UNIFIED_BACKGROUND]   架构简化: 统一使用CSS背景模式，废弃后处理背景合成")

        # 记录关键中间值到metadata（用于debug）
        metadata_collector.record_multiple_values({
            "css_table_left": css_table_left,
            "css_table_top": css_table_top,
            "css_background_width": css_background_width,
            "css_background_height": css_background_height,
            "css_crop_width": css_crop_width,
            "css_crop_height": css_crop_height,
            "css_bg_offset_x": css_bg_offset_x,
            "css_bg_offset_y": css_bg_offset_y,
            "apply_background": apply_background,
            "css_background_applied": css_background_applied,
            "apply_perspective": apply_perspective,
            "perspective_offset_ratio": perspective_offset_ratio,
            "content_area_shrink_ratio": content_area_shrink_ratio,
            "max_scale_factor": max_scale_factor
        })

        return resolved_params

    def _detect_style_conflicts(self, style_config, resolved_header, resolved_body):
        """
        V4.0新增：检测样式冲突并记录警告日志

        Args:
            style_config: 原始样式配置
            resolved_header: 解析后的表头样式
            resolved_body: 解析后的表体样式
        """
        # 检测表头和表体的颜色冲突
        if (resolved_header.text_color == resolved_body.text_color and
            resolved_header.background_color == resolved_body.background_color):
            self.logger.warning(
                f"样式冲突：表头和表体使用了相同的颜色组合 "
                f"(文本色: {resolved_header.text_color}, 背景色: {resolved_header.background_color})"
            )

        # 检测字体冲突
        if (resolved_header.font.default_family == resolved_body.font.default_family and
            resolved_header.font.default_size == resolved_body.font.default_size):
            self.logger.warning(
                f"样式冲突：表头和表体使用了相同的字体配置 "
                f"(字体: {resolved_header.font.default_family}, 大小: {resolved_header.font.default_size}px)"
            )

        # 检测对齐方式冲突
        if (resolved_header.horizontal_align == resolved_body.horizontal_align and
            resolved_header.vertical_align == resolved_body.vertical_align):
            self.logger.warning(
                f"样式冲突：表头和表体使用了相同的对齐方式 "
                f"(水平: {resolved_header.horizontal_align}, 垂直: {resolved_header.vertical_align})"
            )

        # 检测分层样式覆盖冲突
        if style_config.hierarchical:
            hierarchical = style_config.hierarchical

            # 检测列样式覆盖
            if hierarchical.column_styles:
                for col_idx, col_style in hierarchical.column_styles.items():
                    if col_style.text_color or col_style.background_color:
                        self.logger.warning(
                            f"样式冲突：列 {col_idx} 的分层样式覆盖了全局颜色配置"
                        )

            # 检测行样式覆盖
            if hierarchical.row_styles:
                for row_idx, row_style in hierarchical.row_styles.items():
                    if row_style.text_color or row_style.background_color:
                        self.logger.warning(
                            f"样式冲突：行 {row_idx} 的分层样式覆盖了全局颜色配置"
                        )

            # 检测单元格样式覆盖
            if hierarchical.cell_styles:
                for cell_id, cell_style in hierarchical.cell_styles.items():
                    if cell_style.text_color or cell_style.background_color:
                        self.logger.warning(
                            f"样式冲突：单元格 {cell_id} 的分层样式覆盖了全局颜色配置"
                        )

    def _select_background_image(self, background_config, random_state):
        """
        选择背景图路径

        Args:
            background_config: 背景图配置
            random_state: 随机状态

        Returns:
            选中的背景图文件路径
        """
        import os
        import glob

        # 收集所有背景图文件
        all_background_files = []

        self.logger.debug(f"开始扫描背景图目录，共 {len(background_config.background_dirs)} 个目录")

        for i, bg_dir in enumerate(background_config.background_dirs):
            self.logger.debug(f"检查目录 {i+1}: {bg_dir}")
            if not os.path.exists(bg_dir):
                self.logger.warning(f"背景图目录不存在: {bg_dir}")
                continue

            self.logger.debug(f"目录存在: {bg_dir}")

            # 支持常见图片格式
            image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp']
            dir_files = []

            for ext in image_extensions:
                pattern = os.path.join(bg_dir, ext)
                dir_files.extend(glob.glob(pattern))
                # 同时支持大写扩展名
                pattern_upper = os.path.join(bg_dir, ext.upper())
                dir_files.extend(glob.glob(pattern_upper))

            self.logger.debug(f"目录 {bg_dir} 中找到 {len(dir_files)} 个图片文件")
            if dir_files:
                self.logger.debug(f"找到的文件: {dir_files[:5]}...")  # 只显示前5个文件
            else:
                self.logger.warning(f"背景图目录中没有找到图片文件: {bg_dir}")
                continue

            # 根据目录概率权重添加文件
            if i < len(background_config.background_dir_probabilities):
                dir_probability = background_config.background_dir_probabilities[i]
                # 将目录中的每个文件都按目录概率加权
                weighted_files = [(f, dir_probability / len(dir_files)) for f in dir_files]
                all_background_files.extend(weighted_files)
            else:
                # 如果没有指定概率，使用等权重
                equal_weight = 1.0 / len(background_config.background_dirs) / len(dir_files)
                weighted_files = [(f, equal_weight) for f in dir_files]
                all_background_files.extend(weighted_files)

        self.logger.debug(f"总共收集到 {len(all_background_files)} 个背景图文件")
        if not all_background_files:
            raise ValueError("没有找到可用的背景图文件")

        # 根据权重随机选择
        files, weights = zip(*all_background_files)
        # 归一化权重
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]

        # 使用numpy的choice进行加权随机选择
        import numpy as np
        selected_index = random_state.choice(len(files), p=normalized_weights)
        selected_file = files[selected_index]

        # 记录背景图选择信息到metadata
        try:
            # 找到选择的文件属于哪个目录
            selected_directory = None
            for bg_dir in background_config.background_dirs:
                if selected_file.startswith(bg_dir):
                    selected_directory = bg_dir
                    break

            if selected_directory:
                dir_index = background_config.background_dirs.index(selected_directory)
                if dir_index < len(background_config.background_dir_probabilities):
                    selected_dir_probability = background_config.background_dir_probabilities[dir_index]
                    metadata_collector.record_file_selection(
                        category="background",
                        selected_file=selected_file,
                        selected_directory=selected_directory,
                        directory_probability=selected_dir_probability,
                        all_directories=background_config.background_dirs,
                        all_probabilities=background_config.background_dir_probabilities
                    )
        except (ValueError, IndexError, AttributeError) as e:
            self.logger.debug(f"记录背景图选择信息失败: {e}")
            # 继续执行，不影响主流程

        self.logger.debug(f"选择背景图: {selected_file}")
        return selected_file



    def _calculate_css_background_params(self, background_path, background_config, random_state, config):
        """
        计算CSS背景图渲染参数

        Args:
            background_path: 背景图路径
            background_config: 背景图配置
            random_state: 随机状态
            config: 完整配置对象（V5.1新增，用于大表格检测）

        Returns:
            CSS渲染参数字典
        """
        try:
            from PIL import Image

            # V5.1调试：添加详细的方法入口日志和参数验证
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] ✓ 开始计算CSS背景参数")
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 背景图路径: {background_path}")
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] config参数类型: {type(config)}")

            # V5.1关键修复：验证config参数
            if config is None:
                raise ValueError("config参数不能为None")
            if not hasattr(config, 'structure'):
                raise ValueError(f"config对象缺少structure属性，实际属性: {dir(config)}")

            self.logger.info(f"[CSS_BACKGROUND_PARAMS] ✓ config参数验证通过")

            # 获取背景图原始尺寸
            with Image.open(background_path) as img:
                bg_width, bg_height = img.size

            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 原始背景图尺寸: {bg_width}x{bg_height}")

            # V5.1改进：基于表格大小选择处理策略
            structure_config = config.structure

            # V5.1调试：记录原始配置值
            self.logger.debug(f"[CONFIG_DEBUG] 原始配置 - cols: {structure_config.cols}")
            self.logger.debug(f"[CONFIG_DEBUG] 原始配置 - header_rows: {structure_config.header_rows}")
            self.logger.debug(f"[CONFIG_DEBUG] 原始配置 - body_rows: {structure_config.body_rows}")

            cols = self._resolve_config_value(structure_config.cols, 10)
            header_rows = self._resolve_config_value(structure_config.header_rows, 1)
            body_rows = self._resolve_config_value(structure_config.body_rows, 10)
            total_rows = header_rows + body_rows

            # V5.1调试：记录解析后的值
            self.logger.info(f"[CONFIG_RESOLVED] 解析后配置 - cols: {cols}")
            self.logger.info(f"[CONFIG_RESOLVED] 解析后配置 - header_rows: {header_rows}")
            self.logger.info(f"[CONFIG_RESOLVED] 解析后配置 - body_rows: {body_rows}")
            self.logger.info(f"[CONFIG_RESOLVED] 解析后配置 - total_rows: {total_rows}")

            # 判断是否为大表格（V5.3优化：降低触发阈值，使用3行3列基准）
            is_large_table = total_rows >= 3 or cols >= 3

            self.logger.info(f"[LARGE_TABLE_DETECTION] 大表格检测结果: {is_large_table}")
            self.logger.info(f"[LARGE_TABLE_DETECTION] 检测条件: total_rows({total_rows}) >= 3 or cols({cols}) >= 3")
            self.logger.info(f"[LARGE_TABLE_DETECTION] V5.3优化: 使用3行3列基准，提供更大的背景图尺寸")

            if is_large_table:
                # 大表格：使用预拼接背景图策略
                self.logger.info(f"[LARGE_TABLE_PREPROCESSING] ✓ 检测到大表格: {total_rows}行 x {cols}列")
                self.logger.info(f"[LARGE_TABLE_PREPROCESSING] 开始预拼接背景图处理...")

                try:
                    background_path, final_bg_width, final_bg_height = self._prepare_tiled_background(
                        background_path, total_rows, cols, bg_width, bg_height, random_state, background_config
                    )

                    # V5.1关键修复：更新crop尺寸以匹配拼接后的背景图
                    # 这是解决大表格截断问题的核心修复
                    crop_width = final_bg_width
                    crop_height = final_bg_height

                    self.logger.info(f"[LARGE_TABLE_CROP_FIX] ✓ 预拼接背景图处理完成")
                    self.logger.info(f"[LARGE_TABLE_CROP_FIX] 原始背景图: {bg_width}x{bg_height}")
                    self.logger.info(f"[LARGE_TABLE_CROP_FIX] 拼接背景图: {final_bg_width}x{final_bg_height}")
                    self.logger.info(f"[LARGE_TABLE_CROP_FIX] 更新crop尺寸: {crop_width}x{crop_height}")
                    self.logger.info(f"[LARGE_TABLE_CROP_FIX] 新背景图路径: {background_path}")

                    # 跳过后续的crop尺寸计算，直接使用拼接背景图的尺寸
                    skip_crop_calculation = True

                except Exception as e:
                    self.logger.error(f"[LARGE_TABLE_PREPROCESSING] ✗ 预拼接背景图失败: {e}")
                    self.logger.error(f"[LARGE_TABLE_PREPROCESSING] 回退到小表格处理逻辑")
                    # 回退到小表格逻辑
                    is_large_table = False

            if not is_large_table:
                # 小表格：保持原有逻辑，支持装饰性缩放（V5.3：现在只有<3行且<3列的表格）
                self.logger.info(f"[SMALL_TABLE] 小表格使用原有逻辑: {total_rows}行 x {cols}列")
                self.logger.info(f"[SMALL_TABLE] V5.3优化: 只有<3行且<3列的表格才使用小表格逻辑")
                if hasattr(background_config, 'max_scale_factor'):
                    # 小表格保留max_scale_factor的装饰性缩放功能
                    scale_factor = random_state.uniform(1.0, background_config.max_scale_factor)
                    final_bg_width = int(bg_width * scale_factor)
                    final_bg_height = int(bg_height * scale_factor)
                    self.logger.info(f"[SMALL_TABLE] 装饰性缩放: {scale_factor:.2f}倍 -> {final_bg_width}x{final_bg_height}")
                else:
                    final_bg_width, final_bg_height = bg_width, bg_height
                    self.logger.info(f"[SMALL_TABLE] 无缩放，使用原始尺寸: {final_bg_width}x{final_bg_height}")

                # 小表格继续使用原有的crop尺寸计算逻辑
                skip_crop_calculation = False

            # 估算表格尺寸（基于配置参数）
            # 这是一个粗略估算，用于确保有足够空间
            estimated_table_width = self._estimate_table_width(self.current_config)
            estimated_table_height = self._estimate_table_height(self.current_config)

            self.logger.debug(f"估算表格尺寸: {estimated_table_width}x{estimated_table_height}")

            # V5.1改进：动态计算裁剪尺寸，支持超大表格（最大50x50）
            # 现在基于表格实际尺寸动态计算容器尺寸，移除硬编码限制

            # 计算表格所需的最小容器尺寸（包含安全边距）
            min_container_width = estimated_table_width + 200  # 左右各100px边距
            min_container_height = estimated_table_height + 200  # 上下各100px边距

            # V5.3动态限制：基于实际需求设置系统限制，避免硬编码截断问题
            # 设置基础安全限制（避免内存问题）
            BASE_SYSTEM_MAX_WIDTH = 8000   # 基础最大宽度
            BASE_SYSTEM_MAX_HEIGHT = 6000  # 基础最大高度

            # 动态调整系统限制：如果背景拼接后的尺寸超过基础限制，适当提高上限
            dynamic_system_max_width = max(BASE_SYSTEM_MAX_WIDTH, final_bg_width + 2000)
            dynamic_system_max_height = max(BASE_SYSTEM_MAX_HEIGHT, final_bg_height + 2000)

            # 设置绝对上限（防止内存溢出）
            ABSOLUTE_SYSTEM_MAX_WIDTH = 25000   # 绝对最大宽度
            ABSOLUTE_SYSTEM_MAX_HEIGHT = 25000  # 绝对最大高度

            SYSTEM_MAX_WIDTH = min(dynamic_system_max_width, ABSOLUTE_SYSTEM_MAX_WIDTH)
            SYSTEM_MAX_HEIGHT = min(dynamic_system_max_height, ABSOLUTE_SYSTEM_MAX_HEIGHT)

            self.logger.info(f"[DYNAMIC_LIMITS] 背景拼接尺寸: {final_bg_width}x{final_bg_height}")
            self.logger.info(f"[DYNAMIC_LIMITS] 动态系统限制: {SYSTEM_MAX_WIDTH}x{SYSTEM_MAX_HEIGHT}")
            if SYSTEM_MAX_WIDTH > BASE_SYSTEM_MAX_WIDTH or SYSTEM_MAX_HEIGHT > BASE_SYSTEM_MAX_HEIGHT:
                self.logger.info(f"[DYNAMIC_LIMITS] 已提高系统限制以适应大背景图（基础限制: {BASE_SYSTEM_MAX_WIDTH}x{BASE_SYSTEM_MAX_HEIGHT}）")

            # V5.2改进：智能容器尺寸计算，避免过度放大
            if not skip_crop_calculation:
                # V5.2新增：检查表格与背景图的尺寸比例，决定处理策略
                table_bg_width_ratio = estimated_table_width / final_bg_width if final_bg_width > 0 else 1
                table_bg_height_ratio = estimated_table_height / final_bg_height if final_bg_height > 0 else 1

                self.logger.info(f"[CONTAINER_SIZE_V52] 表格与背景图比例: 宽度{table_bg_width_ratio:.2f}, 高度{table_bg_height_ratio:.2f}")

                # V5.2关键修复：避免容器过度放大
                REASONABLE_EXPANSION_LIMIT = 2.5  # 容器最多是表格的2.5倍

                # 计算合理的容器尺寸（基于表格尺寸 + 合理边距）
                target_margin = 100  # 目标边距
                ideal_container_width = estimated_table_width + target_margin * 2
                ideal_container_height = estimated_table_height + target_margin * 2

                # 确保容器不会太小（至少要能容纳背景图的一部分）
                min_container_width = max(ideal_container_width, int(final_bg_width * 0.6))
                min_container_height = max(ideal_container_height, int(final_bg_height * 0.6))

                # V5.2关键修复：限制容器的最大尺寸，避免过度放大
                max_reasonable_width = int(estimated_table_width * REASONABLE_EXPANSION_LIMIT)
                max_reasonable_height = int(estimated_table_height * REASONABLE_EXPANSION_LIMIT)

                # 在合理范围内选择容器尺寸
                max_container_width = min(final_bg_width * 2, max_reasonable_width, SYSTEM_MAX_WIDTH)
                max_container_height = min(final_bg_height * 2, max_reasonable_height, SYSTEM_MAX_HEIGHT)

                # 确保最大值不小于最小值
                max_container_width = max(max_container_width, min_container_width)
                max_container_height = max(max_container_height, min_container_height)

                self.logger.info(f"[CONTAINER_SIZE_V53] 理想容器尺寸: {ideal_container_width}x{ideal_container_height}")
                self.logger.info(f"[CONTAINER_SIZE_V53] 容器尺寸范围: [{min_container_width}, {max_container_width}] x [{min_container_height}, {max_container_height}]")
                self.logger.info(f"[CONTAINER_SIZE_V53] 使用动态系统限制: {SYSTEM_MAX_WIDTH}x{SYSTEM_MAX_HEIGHT}")

                # 动态计算最大裁剪尺寸
                adaptive_max_width = max_container_width
                adaptive_max_height = max_container_height

            # V5.2改进：智能crop尺寸计算策略
            self.logger.info(f"[CROP_CALCULATION_V52] 开始crop尺寸计算，skip_crop_calculation: {skip_crop_calculation}")

            if not skip_crop_calculation:
                # V5.2改进：使用更智能的crop尺寸计算逻辑
                self.logger.info(f"[SMART_CROP_V52] 开始智能crop尺寸计算")

                # V5.2修复：crop尺寸应该在合理范围内，避免过度随机化
                crop_width = random_state.randint(min_container_width, adaptive_max_width + 1)
                crop_height = random_state.randint(min_container_height, adaptive_max_height + 1)

                self.logger.info(f"[SMART_CROP_V52] 智能计算crop尺寸: {crop_width}x{crop_height}")
                self.logger.info(f"[SMART_CROP_V52] 计算范围 - 宽度: [{min_container_width}, {adaptive_max_width}]")
                self.logger.info(f"[SMART_CROP_V52] 计算范围 - 高度: [{min_container_height}, {adaptive_max_height}]")
            else:
                # 大表格：crop尺寸已经在前面设置为拼接背景图的尺寸，无需重新计算
                self.logger.info(f"[LARGE_TABLE_CROP_FIX] ✓ 跳过crop尺寸计算")
                self.logger.info(f"[LARGE_TABLE_CROP_FIX] 使用拼接背景图尺寸作为crop尺寸: {crop_width}x{crop_height}")

            # 记录大表格处理信息
            if estimated_table_width > 2000 or estimated_table_height > 1500:
                self.logger.info(f"[LARGE_TABLE] 检测到大表格: {estimated_table_width}x{estimated_table_height}")
                self.logger.info(f"[LARGE_TABLE] 动态容器尺寸: {crop_width}x{crop_height}")
                self.logger.info(f"[LARGE_TABLE] 系统限制: {SYSTEM_MAX_WIDTH}x{SYSTEM_MAX_HEIGHT}")

            # 计算表格在背景中的位置（随机），确保不会截断
            max_table_left = max(50, crop_width - estimated_table_width - 50)
            max_table_top = max(50, crop_height - estimated_table_height - 50)

            # V5.1改进：增强的尺寸验证逻辑，支持大表格
            if max_table_left < 50:
                max_table_left = 50
                if estimated_table_width > 2000:
                    self.logger.info(f"[LARGE_TABLE] 大表格宽度 {estimated_table_width} 需要容器宽度 {crop_width}")
                else:
                    self.logger.warning(f"裁剪宽度 {crop_width} 可能不足以容纳表格宽度 {estimated_table_width}")

            if max_table_top < 50:
                max_table_top = 50
                if estimated_table_height > 1500:
                    self.logger.info(f"[LARGE_TABLE] 大表格高度 {estimated_table_height} 需要容器高度 {crop_height}")
                else:
                    self.logger.warning(f"裁剪高度 {crop_height} 可能不足以容纳表格高度 {estimated_table_height}")

            # 检查是否超出系统限制
            if crop_width > SYSTEM_MAX_WIDTH or crop_height > SYSTEM_MAX_HEIGHT:
                self.logger.warning(f"[LARGE_TABLE] 表格尺寸接近系统限制: {crop_width}x{crop_height}")
                self.logger.warning(f"[LARGE_TABLE] 系统安全限制: {SYSTEM_MAX_WIDTH}x{SYSTEM_MAX_HEIGHT}")
                self.logger.warning(f"[LARGE_TABLE] 如果渲染失败，请考虑减少表格行列数")

            # 使用智能位置分布计算表格位置
            table_left, table_top = self._calculate_smart_table_position(
                estimated_table_width, estimated_table_height,
                crop_width, crop_height,
                background_config.prefer_center_probability,
                random_state
            )

            self.logger.debug(f"表格完整渲染: 裁剪尺寸={crop_width}x{crop_height}, 表格位置=({table_left}, {table_top})")

            # 计算背景图的随机裁剪偏移
            # 确保背景图的可见部分仍然覆盖整个容器
            bg_offset_x = 0
            bg_offset_y = 0

            if final_bg_width > crop_width:
                # 最大偏移应该确保背景图右边缘不超出容器右边缘
                max_bg_offset_x = final_bg_width - crop_width
                # 使用更保守的偏移范围，确保背景图可见
                safe_max_offset_x = min(max_bg_offset_x, final_bg_width // 4)  # 最多偏移背景图宽度的1/4
                bg_offset_x = random_state.randint(0, safe_max_offset_x)
                self.logger.debug(f"背景图X偏移: {bg_offset_x} (最大安全偏移: {safe_max_offset_x})")

            if final_bg_height > crop_height:
                max_bg_offset_y = final_bg_height - crop_height
                safe_max_offset_y = min(max_bg_offset_y, final_bg_height // 4)  # 最多偏移背景图高度的1/4
                bg_offset_y = random_state.randint(0, safe_max_offset_y)
                self.logger.debug(f"背景图Y偏移: {bg_offset_y} (最大安全偏移: {safe_max_offset_y})")

            params = {
                'background_width': final_bg_width,
                'background_height': final_bg_height,
                'table_left': table_left,
                'table_top': table_top,
                'crop_width': crop_width,
                'crop_height': crop_height,
                'bg_offset_x': bg_offset_x,
                'bg_offset_y': bg_offset_y
            }

            # V5.1调试：详细记录最终CSS参数
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] ✓ CSS背景参数计算完成")
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 最终背景图尺寸: {final_bg_width}x{final_bg_height}")
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 最终crop尺寸: {crop_width}x{crop_height}")
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 表格定位: ({table_left}, {table_top})")
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 背景偏移: ({bg_offset_x}, {bg_offset_y})")
            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 背景图路径: {background_path}")

            # 添加背景图路径到参数中
            params['background_path'] = background_path

            self.logger.debug(f"CSS背景参数完整信息: {params}")
            return params

        except Exception as e:
            import traceback
            self.logger.error(f"[CSS_BACKGROUND_PARAMS] ✗ 计算CSS背景参数失败: {e}")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS] 错误类型: {type(e).__name__}")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS] 错误详情: {traceback.format_exc()}")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS] 方法参数:")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS]   - background_path: {background_path}")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS]   - background_config: {type(background_config)}")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS]   - random_state: {type(random_state)}")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS]   - config: {type(config) if config else 'None'}")
            self.logger.error(f"[CSS_BACKGROUND_PARAMS] 回退到默认参数")

            # V5.1改进：返回支持大表格的默认参数
            default_params = {
                'background_width': 3000,  # 增大以支持大表格
                'background_height': 2000, # 增大以支持大表格
                'table_left': 200,
                'table_top': 150,
                'crop_width': 2500,        # 增大以支持大表格
                'crop_height': 1500,       # 增大以支持大表格
                'bg_offset_x': 50,         # 更保守的偏移
                'bg_offset_y': 50,         # 更保守的偏移
                'background_path': background_path  # 保持原始背景图路径
            }

            self.logger.info(f"[CSS_BACKGROUND_PARAMS] 使用默认参数: {default_params}")
            return default_params

    def _estimate_table_width(self, config) -> int:
        """
        V5.1改进：更精确的表格宽度估算，支持50x50大表格

        基于配置参数和内容特征更准确地估算表格的渲染宽度
        """
        # 获取表格结构配置
        structure_config = config.structure
        # V5.1修复：解析配置值，处理ProbabilisticRange类型
        cols = self._resolve_config_value(structure_config.cols, 10)

        # 获取样式配置以更准确估算
        style_config = config.style

        # 1. 更精确的列宽估算
        col_width = self._estimate_realistic_column_width(config)

        # 2. 考虑合并单元格的影响
        merge_factor = self._calculate_merge_impact_factor(structure_config.merge_probability)

        # 3. 考虑边框样式的影响
        border_width = self._estimate_border_width(style_config)

        # 4. 考虑内边距的真实影响
        padding = self._get_realistic_padding(style_config.common.padding)

        # 5. 精确计算基础宽度
        base_width = cols * (col_width + padding * 2) + (cols + 1) * border_width

        # 6. 考虑字体渲染的实际差异
        font_factor = self._calculate_font_width_factor(style_config.common.font)

        # 7. 应用字体和合并因子
        estimated_width = int(base_width * font_factor * merge_factor)

        # 8. 为超大表格提供更大的安全边距
        if cols > 30:
            safety_factor = 1.5  # 超大表格需要更大安全边距
            self.logger.debug(f"[WIDTH_ESTIMATION] 超大表格({cols}列)，使用安全系数: {safety_factor}")
        elif cols > 20:
            safety_factor = 1.4  # 大表格需要更大安全边距
            self.logger.debug(f"[WIDTH_ESTIMATION] 大表格({cols}列)，使用安全系数: {safety_factor}")
        elif cols > 10:
            safety_factor = 1.3
        else:
            safety_factor = 1.2

        final_width = int(estimated_width * safety_factor)

        # 9. 设置合理的最小值
        min_width = 500 if cols <= 10 else 800
        result = max(final_width, min_width)

        self.logger.debug(f"[WIDTH_ESTIMATION] 列数: {cols}, 基础列宽: {col_width}, 内边距: {padding}")
        self.logger.debug(f"[WIDTH_ESTIMATION] 边框宽度: {border_width}, 字体因子: {font_factor:.2f}, 合并因子: {merge_factor:.2f}")
        self.logger.debug(f"[WIDTH_ESTIMATION] 基础宽度: {base_width}, 最终宽度: {result}")

        return result

    def _estimate_table_height(self, config) -> int:
        """
        V5.1改进：更精确的表格高度估算，支持50x50大表格

        基于配置参数和内容特征更准确地估算表格的渲染高度
        """
        # 获取表格结构配置
        structure_config = config.structure
        # V5.1修复：解析配置值，处理ProbabilisticRange类型
        header_rows = self._resolve_config_value(structure_config.header_rows, 1)
        body_rows = self._resolve_config_value(structure_config.body_rows, 10)
        total_rows = header_rows + body_rows

        # 获取样式配置以更准确估算
        style_config = config.style

        # 1. 更精确的行高估算
        row_height = self._estimate_realistic_row_height(config)

        # 2. 考虑边框样式的影响
        border_width = self._estimate_border_width(style_config)

        # 3. 头部行通常更高（字体更大或加粗）
        header_height = int(row_height * 1.3) if header_rows > 0 else row_height

        # 4. 考虑内容溢出处理策略的影响
        overflow_factor = self._calculate_overflow_impact_factor(style_config)

        # 5. 精确计算基础高度
        base_height = (header_height * header_rows +
                      row_height * body_rows +
                      (total_rows + 1) * border_width)

        # 6. 应用溢出因子（wrap模式可能增加行高）
        estimated_height = int(base_height * overflow_factor)

        # 7. 为超大表格提供更大的安全边距
        if total_rows > 40:
            safety_factor = 1.6  # 超大表格需要更大安全边距
            self.logger.debug(f"[HEIGHT_ESTIMATION] 超大表格({total_rows}行)，使用安全系数: {safety_factor}")
        elif total_rows > 25:
            safety_factor = 1.5  # 大表格需要更大安全边距
            self.logger.debug(f"[HEIGHT_ESTIMATION] 大表格({total_rows}行)，使用安全系数: {safety_factor}")
        elif total_rows > 15:
            safety_factor = 1.4
        else:
            safety_factor = 1.3

        final_height = int(estimated_height * safety_factor)

        # 8. 设置合理的最小值
        min_height = 400 if total_rows <= 10 else 600
        result = max(final_height, min_height)

        self.logger.debug(f"[HEIGHT_ESTIMATION] 行数: {total_rows}(头:{header_rows},体:{body_rows})")
        self.logger.debug(f"[HEIGHT_ESTIMATION] 基础行高: {row_height}, 头部行高: {header_height}")
        self.logger.debug(f"[HEIGHT_ESTIMATION] 边框宽度: {border_width}, 溢出因子: {overflow_factor:.2f}")
        self.logger.debug(f"[HEIGHT_ESTIMATION] 基础高度: {base_height}, 最终高度: {result}")

        return result

    def _calculate_smart_table_position(self, table_width: int, table_height: int,
                                      container_width: int, container_height: int,
                                      prefer_center_prob: float, random_state) -> tuple:
        """
        智能位置分布：让表格尽可能在背景图的中间位置
        V5.2修复：解决大表格在超大容器中被错误定位到边缘的问题

        Args:
            table_width: 表格宽度
            table_height: 表格高度
            container_width: 容器宽度
            container_height: 容器高度
            prefer_center_prob: 偏向中心的概率
            random_state: 随机状态

        Returns:
            (table_left, table_top): 表格位置
        """
        # V5.2新增：检测容器与表格的尺寸比例，判断是否需要特殊处理
        width_ratio = container_width / table_width if table_width > 0 else 1
        height_ratio = container_height / table_height if table_height > 0 else 1

        self.logger.info(f"[SMART_POSITION] 尺寸比例分析: 容器{container_width}x{container_height}, 表格{table_width}x{table_height}")
        self.logger.info(f"[SMART_POSITION] 宽度比例: {width_ratio:.2f}, 高度比例: {height_ratio:.2f}")

        # V5.2关键修复：当容器远大于表格时，使用中心定位策略避免表格被放到边缘
        LARGE_CONTAINER_THRESHOLD = 3.0  # 当容器是表格的3倍以上时，认为是大容器

        if width_ratio > LARGE_CONTAINER_THRESHOLD or height_ratio > LARGE_CONTAINER_THRESHOLD:
            self.logger.info(f"[SMART_POSITION] 检测到大容器场景，使用中心定位策略")
            return self._calculate_center_focused_position(
                table_width, table_height, container_width, container_height, random_state
            )

        # 正常情况：使用原有的边距逻辑（保持兼容性）
        max_table_left = max(0, container_width - table_width - 100)  # 留100px右边距
        max_table_top = max(0, container_height - table_height - 100)  # 留100px下边距

        safe_left = 50  # 50px左边距
        safe_top = 50   # 50px上边距
        safe_right = max_table_left
        safe_bottom = max_table_top

        # 检查是否有足够空间
        if safe_left >= safe_right or safe_top >= safe_bottom:
            self.logger.warning(f"[SMART_POSITION] 容器空间不足，使用最小边距")
            safe_left = min(20, container_width // 20)
            safe_top = min(20, container_height // 20)
            safe_right = max(safe_left + 1, container_width - table_width - safe_left)
            safe_bottom = max(safe_top + 1, container_height - table_height - safe_top)

        self.logger.info(f"[SMART_POSITION] 安全区域: left={safe_left}, top={safe_top}, right={safe_right}, bottom={safe_bottom}")

        # 决定使用中心偏向还是均匀分布
        if random_state.random() < prefer_center_prob:
            # 使用中心偏向分布
            pos_x, pos_y = self._calculate_center_biased_position(
                safe_left, safe_top, safe_right, safe_bottom, random_state
            )
            self.logger.info(f"[SMART_POSITION] 使用中心偏向分布: ({pos_x}, {pos_y})")
        else:
            # 使用均匀分布
            pos_x = random_state.randint(safe_left, safe_right + 1)
            pos_y = random_state.randint(safe_top, safe_bottom + 1)
            self.logger.info(f"[SMART_POSITION] 使用均匀分布: ({pos_x}, {pos_y})")

        return (int(pos_x), int(pos_y))

    def _calculate_center_focused_position(self, table_width: int, table_height: int,
                                         container_width: int, container_height: int,
                                         random_state) -> tuple:
        """
        V5.2新增：大容器场景下的中心定位策略

        当容器远大于表格时，强制使用中心定位 + 小范围随机偏移，
        避免表格被随机定位到容器边缘。

        Args:
            table_width: 表格宽度
            table_height: 表格高度
            container_width: 容器宽度
            container_height: 容器高度
            random_state: 随机状态

        Returns:
            (table_left, table_top): 表格位置
        """
        # 计算容器中心位置
        center_x = container_width // 2 - table_width // 2
        center_y = container_height // 2 - table_height // 2

        # 计算允许的偏移范围（限制在合理范围内）
        max_offset_x = min(200, container_width // 10, (container_width - table_width) // 4)
        max_offset_y = min(200, container_height // 10, (container_height - table_height) // 4)

        # 确保偏移后的位置仍在容器内
        max_offset_x = min(max_offset_x, center_x, container_width - center_x - table_width)
        max_offset_y = min(max_offset_y, center_y, container_height - center_y - table_height)

        # 生成随机偏移
        offset_x = random_state.randint(-max_offset_x, max_offset_x + 1)
        offset_y = random_state.randint(-max_offset_y, max_offset_y + 1)

        # 计算最终位置
        final_x = center_x + offset_x
        final_y = center_y + offset_y

        # V5.2增强：更严格的安全边界检查
        final_x = max(20, min(final_x, container_width - table_width - 20))
        final_y = max(20, min(final_y, container_height - table_height - 20))

        # V5.2新增：验证最终位置的合理性
        if final_x < 0 or final_y < 0:
            self.logger.error(f"[CENTER_FOCUSED] 计算出负坐标位置: ({final_x}, {final_y})")
            # 回退到安全的中心位置
            final_x = max(20, center_x)
            final_y = max(20, center_y)
            self.logger.warning(f"[CENTER_FOCUSED] 回退到安全中心位置: ({final_x}, {final_y})")

        if final_x + table_width > container_width or final_y + table_height > container_height:
            self.logger.error(f"[CENTER_FOCUSED] 表格超出容器边界")
            self.logger.error(f"[CENTER_FOCUSED] 表格区域: ({final_x}, {final_y}) - ({final_x + table_width}, {final_y + table_height})")
            self.logger.error(f"[CENTER_FOCUSED] 容器尺寸: {container_width}x{container_height}")
            # 重新计算安全位置
            final_x = min(final_x, container_width - table_width - 20)
            final_y = min(final_y, container_height - table_height - 20)
            self.logger.warning(f"[CENTER_FOCUSED] 修正后位置: ({final_x}, {final_y})")

        self.logger.info(f"[CENTER_FOCUSED] 中心位置: ({center_x}, {center_y})")
        self.logger.info(f"[CENTER_FOCUSED] 偏移范围: ±{max_offset_x}, ±{max_offset_y}")
        self.logger.info(f"[CENTER_FOCUSED] 随机偏移: ({offset_x}, {offset_y})")
        self.logger.info(f"[CENTER_FOCUSED] 最终位置: ({final_x}, {final_y})")
        self.logger.info(f"[CENTER_FOCUSED] 表格在容器中的区域: ({final_x}, {final_y}) - ({final_x + table_width}, {final_y + table_height})")

        return (int(final_x), int(final_y))

    def _calculate_center_biased_position(self, safe_left: int, safe_top: int,
                                        safe_right: int, safe_bottom: int, random_state) -> tuple:
        """
        计算中心偏向的位置

        使用正态分布，让位置更倾向于出现在中心区域
        """
        import math

        # 计算中心点
        center_x = (safe_left + safe_right) / 2
        center_y = (safe_top + safe_bottom) / 2

        # 计算标准差（固定偏向强度）
        range_x = safe_right - safe_left
        range_y = safe_bottom - safe_top

        # 使用固定的中心偏向强度：让大部分位置集中在中心区域
        std_x = range_x / 4  # 约68%的值在中心1/2区域内
        std_y = range_y / 4

        # 生成正态分布的位置
        # 使用Box-Muller变换生成正态分布随机数
        u1 = random_state.random()
        u2 = random_state.random()

        # Box-Muller变换
        z0 = math.sqrt(-2 * math.log(u1)) * math.cos(2 * math.pi * u2)
        z1 = math.sqrt(-2 * math.log(u1)) * math.sin(2 * math.pi * u2)

        # 应用到位置计算
        pos_x = center_x + z0 * std_x
        pos_y = center_y + z1 * std_y

        # 确保位置在安全区域内
        pos_x = max(safe_left, min(safe_right, pos_x))
        pos_y = max(safe_top, min(safe_bottom, pos_y))

        self.logger.debug(f"[CENTER_BIAS] 中心点: ({center_x}, {center_y}), 标准差: ({std_x:.1f}, {std_y:.1f})")
        self.logger.debug(f"[CENTER_BIAS] 生成位置: ({pos_x:.1f}, {pos_y:.1f})")

        return (pos_x, pos_y)

    def _select_perspective_offset_ratio(self, perspective_config, random_state, config=None, resolved_structure=None) -> float:
        """
        选择透视变换强度比例

        优先使用range_list配置，如果没有则回退到max_offset_ratio（向后兼容）
        V5.1新增：支持基于表格大小的动态缩放

        Args:
            perspective_config: 透视变换配置
            random_state: 随机状态
            config: 完整配置对象（用于动态缩放）
            resolved_structure: 已解析的结构参数（用于获取实际表格尺寸）

        Returns:
            选择的透视变换强度比例
        """
        # 优先使用新的range_list配置
        if perspective_config.range_list and perspective_config.probability_list:
            base_ratio = self._select_from_perspective_range_list(
                perspective_config.range_list,
                perspective_config.probability_list,
                random_state
            )
        # 向后兼容：使用旧的max_offset_ratio配置
        elif perspective_config.max_offset_ratio is not None:
            self.logger.info(f"[PERSPECTIVE] 使用向后兼容的max_offset_ratio: {perspective_config.max_offset_ratio}")
            base_ratio = perspective_config.max_offset_ratio
        else:
            # 默认值
            base_ratio = 0.03
            self.logger.warning(f"[PERSPECTIVE] 未找到透视变换强度配置，使用默认值: {base_ratio}")

        # V5.1新增：应用动态缩放
        final_ratio = self._apply_adaptive_scaling(base_ratio, perspective_config, config, resolved_structure)

        # 确保基础选择信息被记录到最终的调试信息中
        if hasattr(self, '_perspective_adaptive_debug') and hasattr(self, '_perspective_base_selection_debug'):
            if 'base_selection' not in self._perspective_adaptive_debug:
                self._perspective_adaptive_debug['base_selection'] = self._perspective_base_selection_debug
        elif hasattr(self, '_perspective_base_selection_debug'):
            # 如果没有adaptive_scaling，只记录基础选择信息
            self._perspective_adaptive_debug = {
                'base_ratio': base_ratio,
                'final_ratio': final_ratio,
                'adaptive_scaling_applied': False,
                'reason': 'adaptive_scaling未应用，使用基础强度',
                'base_selection': self._perspective_base_selection_debug
            }

        return final_ratio

    def _apply_adaptive_scaling(self, base_ratio, perspective_config, config, resolved_structure=None):
        """
        应用透视变换动态缩放

        Args:
            base_ratio: 基础透视变换强度比例
            perspective_config: 透视变换配置
            config: 完整配置对象
            resolved_structure: 已解析的结构参数（包含实际的表格尺寸）

        Returns:
            应用缩放后的透视变换强度比例
        """
        # 检查是否启用动态缩放
        if not hasattr(perspective_config, 'adaptive_scaling') or not perspective_config.adaptive_scaling:
            # 记录未应用adaptive_scaling的情况
            self._perspective_adaptive_debug = {
                'base_ratio': base_ratio,
                'final_ratio': base_ratio,
                'adaptive_scaling_applied': False,
                'reason': 'adaptive_scaling配置未启用或不存在'
            }
            return base_ratio

        if not config or not hasattr(config, 'structure'):
            # 记录config缺失的情况
            self._perspective_adaptive_debug = {
                'base_ratio': base_ratio,
                'final_ratio': base_ratio,
                'adaptive_scaling_applied': False,
                'reason': 'config参数缺失或无structure属性'
            }
            return base_ratio

        try:
            # V5.1修复：使用实际解析后的表格尺寸，而不是配置的平均值
            if resolved_structure:
                # 优先使用已解析的结构参数（实际值）
                cols = resolved_structure.cols
                total_rows = resolved_structure.header_rows + resolved_structure.body_rows
                self.logger.debug(f"[PERSPECTIVE_ADAPTIVE] 使用已解析的表格尺寸: {total_rows}行 x {cols}列")
            else:
                # 回退到配置解析（但这会导致不准确的结果）
                structure_config = config.structure
                cols = self._resolve_config_value(structure_config.cols, 10)
                header_rows = self._resolve_config_value(structure_config.header_rows, 1)
                body_rows = self._resolve_config_value(structure_config.body_rows, 10)
                total_rows = header_rows + body_rows
                self.logger.warning(f"[PERSPECTIVE_ADAPTIVE] 使用配置平均值（不准确）: {total_rows}行 x {cols}列")

            # 计算表格最大维度
            max_dimension = max(total_rows, cols)

            # 解析adaptive_scaling配置
            adaptive_scaling = perspective_config.adaptive_scaling
            if len(adaptive_scaling) != 2 or len(adaptive_scaling[0]) != 2 or len(adaptive_scaling[1]) != 2:
                self.logger.warning(f"[PERSPECTIVE_ADAPTIVE] adaptive_scaling配置格式错误，跳过动态缩放")
                return base_ratio

            min_table_size, max_scale_factor = adaptive_scaling[0]
            max_table_size, min_scale_factor = adaptive_scaling[1]
            decay_rate = getattr(perspective_config, 'decay_rate', 1.8)

            # 计算缩放系数
            scale_factor = self._calculate_exponential_decay_scale_factor(
                max_dimension, min_table_size, max_table_size,
                min_scale_factor, max_scale_factor, decay_rate
            )

            # 应用缩放
            final_ratio = base_ratio * scale_factor

            # 记录详细的adaptive_scaling调试信息到实例变量中
            self._perspective_adaptive_debug = {
                'table_rows': total_rows,
                'table_cols': cols,
                'max_dimension': max_dimension,
                'base_ratio': base_ratio,
                'scale_factor': scale_factor,
                'final_ratio': final_ratio,
                'min_table_size': min_table_size,
                'max_table_size': max_table_size,
                'min_scale_factor': min_scale_factor,
                'max_scale_factor': max_scale_factor,
                'decay_rate': decay_rate,
                'adaptive_scaling_applied': True,
                'using_resolved_structure': resolved_structure is not None
            }

            # 合并基础选择信息（如果存在）
            if hasattr(self, '_perspective_base_selection_debug'):
                self._perspective_adaptive_debug['base_selection'] = self._perspective_base_selection_debug

            self.logger.info(f"[PERSPECTIVE_ADAPTIVE] 表格尺寸: {total_rows}x{cols}, 最大维度: {max_dimension}")
            self.logger.info(f"[PERSPECTIVE_ADAPTIVE] 缩放系数: {scale_factor:.3f}, 基础强度: {base_ratio:.4f}, 最终强度: {final_ratio:.4f}")

            return final_ratio

        except Exception as e:
            # 记录异常情况
            self._perspective_adaptive_debug = {
                'base_ratio': base_ratio,
                'final_ratio': base_ratio,
                'adaptive_scaling_applied': False,
                'reason': f'计算异常: {str(e)}'
            }
            self.logger.error(f"[PERSPECTIVE_ADAPTIVE] 动态缩放计算失败: {e}")
            return base_ratio

    def _calculate_exponential_decay_scale_factor(self, table_size, min_size, max_size, min_scale, max_scale, decay_rate):
        """
        计算指数衰减缩放系数

        Args:
            table_size: 表格尺寸（最大维度）
            min_size: 最小表格尺寸
            max_size: 最大表格尺寸
            min_scale: 最小缩放系数
            max_scale: 最大缩放系数
            decay_rate: 衰减率

        Returns:
            计算出的缩放系数
        """
        import math

        if table_size <= min_size:
            return max_scale
        elif table_size >= max_size:
            return min_scale
        else:
            # 指数衰减
            normalized_size = (table_size - min_size) / (max_size - min_size)
            decay_factor = math.pow(normalized_size, decay_rate)
            scale_factor = max_scale - decay_factor * (max_scale - min_scale)
            return scale_factor

    def _select_from_perspective_range_list(self, range_list: List[List[float]],
                                          probability_list: List[float], random_state) -> float:
        """
        从透视变换强度范围列表中选择一个值

        Args:
            range_list: 强度范围列表
            probability_list: 对应的概率列表
            random_state: 随机状态

        Returns:
            选择的透视变换强度值
        """
        # 归一化概率
        total_prob = sum(probability_list)
        if total_prob == 0:
            # 如果所有概率都是0，使用均匀分布
            normalized_probs = [1.0 / len(probability_list)] * len(probability_list)
        else:
            normalized_probs = [p / total_prob for p in probability_list]

        # 使用加权随机选择范围
        import numpy as np
        selected_index = random_state.choice(len(range_list), p=normalized_probs)
        selected_range = range_list[selected_index]

        # 在选定范围内随机选择具体值
        min_offset, max_offset = selected_range
        selected_offset = random_state.uniform(min_offset, max_offset)

        # 记录基础强度选择的调试信息
        self._perspective_base_selection_debug = {
            'selected_range': selected_range,
            'selected_index': selected_index,
            'range_probability': probability_list[selected_index],
            'selected_offset': selected_offset,
            'all_ranges': range_list,
            'all_probabilities': probability_list
        }

        self.logger.info(f"[PERSPECTIVE] 选择强度范围: {selected_range} (概率: {probability_list[selected_index]:.2f})")
        self.logger.info(f"[PERSPECTIVE] 最终强度值: {selected_offset:.4f}")

        return selected_offset

    # V5.1新增：配置值解析辅助方法

    def _resolve_config_value(self, value, default_value=None):
        """
        解析配置值，处理ProbabilisticRange等复杂类型

        Args:
            value: 配置值（可能是数值、ProbabilisticRange等）
            default_value: 默认值

        Returns:
            解析后的数值
        """
        if value is None:
            return default_value

        # 如果是数值类型，直接返回
        if isinstance(value, (int, float)):
            return value

        # 如果是ProbabilisticRange类型，取中位数
        if hasattr(value, 'range_list') and value.range_list:
            # 取所有范围的中位数
            all_values = []
            for range_item in value.range_list:
                if isinstance(range_item, (list, tuple)) and len(range_item) >= 2:
                    all_values.extend(range_item)
                else:
                    all_values.append(range_item)

            if all_values:
                return sum(all_values) / len(all_values)

        # 如果有其他属性，尝试提取数值
        if hasattr(value, '__dict__'):
            # 尝试常见的数值属性
            for attr in ['value', 'default', 'min', 'max']:
                if hasattr(value, attr):
                    attr_value = getattr(value, attr)
                    if isinstance(attr_value, (int, float)):
                        return attr_value

        # 尝试转换为数值
        try:
            return float(value)
        except (ValueError, TypeError):
            pass

        # 如果都失败了，返回默认值
        self.logger.warning(f"[CONFIG_RESOLVE] 无法解析配置值: {value}, 使用默认值: {default_value}")
        return default_value

    # V5.1新增：表格尺寸估算辅助方法

    def _estimate_realistic_column_width(self, config):
        """
        基于内容类型和字体大小估算真实列宽
        """
        # 获取样式配置
        style_config = config.style

        # 获取有效字体大小
        font_size = self._get_effective_font_size(style_config.common.font)

        # 分析内容源，预测平均内容长度
        avg_content_length = self._analyze_content_length(config.content)

        # 基于字符数和字体大小计算列宽
        char_width = font_size * 0.6  # 经验值：字符宽度约为字体大小的0.6倍
        estimated_col_width = avg_content_length * char_width

        # 设置合理的最小和最大值
        min_width = 80
        max_width = 300

        return max(min_width, min(estimated_col_width, max_width))

    def _estimate_realistic_row_height(self, config):
        """
        基于字体大小和样式估算真实行高
        """
        style_config = config.style

        # 获取有效字体大小
        font_size = self._get_effective_font_size(style_config.common.font)

        # 获取真实内边距
        padding = self._get_realistic_padding(style_config.common.padding)

        # 考虑内容溢出处理策略
        overflow_strategy = getattr(style_config, 'overflow_strategy', 'wrap')

        if overflow_strategy == 'wrap':
            # wrap模式可能增加行高
            base_height = int(font_size * 1.6 + padding * 2 + 6)  # 更大的行高
        else:
            # truncate模式保持固定行高
            base_height = int(font_size * 1.4 + padding * 2 + 4)

        return base_height

    def _get_effective_font_size(self, font_config):
        """
        V5.1改进：获取有效的字体大小，使用统一的配置解析
        """
        default_size = getattr(font_config, 'default_size', 14)
        return self._resolve_config_value(default_size, 14)

    def _get_realistic_padding(self, padding_config):
        """
        V5.1改进：获取真实的内边距值，使用统一的配置解析
        """
        return self._resolve_config_value(padding_config, 8)

    def _analyze_content_length(self, content_config):
        """
        分析内容源，预测平均内容长度
        """
        if hasattr(content_config, 'source_type'):
            if content_config.source_type == 'csv':
                # CSV内容通常较短
                return 12  # 平均12个字符
            elif content_config.source_type == 'programmatic':
                # 程序化内容根据类型估算
                if hasattr(content_config, 'programmatic_types'):
                    types = content_config.programmatic_types
                    if 'currency' in types or 'percentage' in types:
                        return 10  # 数值类型
                    elif 'date' in types:
                        return 12  # 日期类型
                    elif 'email' in types:
                        return 20  # 邮箱类型
                    else:
                        return 15  # 文本类型
                else:
                    return 12

        return 12  # 默认值

    def _calculate_merge_impact_factor(self, merge_probability):
        """
        计算合并单元格对宽度的影响因子
        """
        # V5.1改进：使用统一的配置解析
        avg_merge_prob = self._resolve_config_value(merge_probability, 0.1)

        # 合并单元格通常会减少总宽度需求
        return 1.0 - (avg_merge_prob * 0.2)  # 最多减少20%



    def _prepare_tiled_background(self, background_path, total_rows, cols, bg_width, bg_height, random_state, background_config):
        """
        V5.1新增：为大表格准备拼接背景图

        Args:
            background_path: 原始背景图路径
            total_rows: 表格总行数
            cols: 表格列数
            bg_width: 原始背景图宽度
            bg_height: 原始背景图高度
            random_state: 随机状态
            background_config: 背景配置

        Returns:
            (tiled_background_path, final_width, final_height): 拼接后的背景图信息
        """
        # V5.3优化：计算需要的网格尺寸（改为3行3列基准）
        grid_cols = max(1, (cols + 2) // 3)  # 每3列需要1个背景单元，向上取整
        grid_rows = max(1, (total_rows + 2) // 3)  # 每3行需要1个背景单元，向上取整

        self.logger.info(f"[BACKGROUND_TILING] ✓ 开始背景图拼接处理（V5.3优化：3行3列基准）")
        self.logger.info(f"[BACKGROUND_TILING] 表格尺寸: {total_rows}行 x {cols}列")
        self.logger.info(f"[BACKGROUND_TILING] 计算网格: {grid_rows}x{grid_cols} = {grid_rows * grid_cols}个背景单元")
        self.logger.info(f"[BACKGROUND_TILING] 原始背景: {bg_width}x{bg_height}")
        self.logger.info(f"[BACKGROUND_TILING] 网格计算公式: cols({cols}+2)//3={grid_cols}, rows({total_rows}+2)//3={grid_rows}")
        self.logger.info(f"[BACKGROUND_TILING] 拼接后尺寸: {bg_width * grid_cols}x{bg_height * grid_rows}")

        # 检查是否需要拼接（如果只需要1x1，直接使用原图）
        if grid_rows == 1 and grid_cols == 1:
            # 单个背景单元，但可能需要适度缩放以确保容纳表格
            scale_factor = self._calculate_safe_scale_for_single_background(
                total_rows, cols, bg_width, bg_height, background_config, random_state
            )
            final_width = int(bg_width * scale_factor)
            final_height = int(bg_height * scale_factor)

            if scale_factor > 1.0:
                # 需要缩放，创建缩放后的背景图
                scaled_background_path = self._create_scaled_background(
                    background_path, final_width, final_height
                )
                return scaled_background_path, final_width, final_height
            else:
                # 不需要缩放，直接使用原图
                return background_path, bg_width, bg_height

        # 需要拼接多个背景单元
        tiled_background_path = self._create_tiled_background(
            background_path, grid_rows, grid_cols, bg_width, bg_height
        )

        # 计算拼接后的尺寸
        final_width = bg_width * grid_cols
        final_height = bg_height * grid_rows

        self.logger.info(f"[BACKGROUND_TILING] 拼接完成: {final_width}x{final_height}")

        return tiled_background_path, final_width, final_height

    def _create_tiled_background(self, background_path, grid_rows, grid_cols, tile_width, tile_height):
        """
        创建拼接背景图

        Args:
            background_path: 原始背景图路径
            grid_rows: 网格行数
            grid_cols: 网格列数
            tile_width: 单个瓦片宽度
            tile_height: 单个瓦片高度

        Returns:
            拼接后的背景图路径
        """
        from PIL import Image
        import os
        import tempfile
        import uuid

        try:
            # 加载原始背景图
            base_image = Image.open(background_path)

            # 确保原始图像尺寸正确
            if base_image.size != (tile_width, tile_height):
                base_image = base_image.resize((tile_width, tile_height), Image.Resampling.LANCZOS)

            # 计算拼接后的总尺寸
            total_width = tile_width * grid_cols
            total_height = tile_height * grid_rows

            self.logger.info(f"[TILING] ✓ 开始创建拼接背景图")
            self.logger.info(f"[TILING] 单个瓦片尺寸: {tile_width}x{tile_height}")
            self.logger.info(f"[TILING] 网格布局: {grid_rows}行 x {grid_cols}列")
            self.logger.info(f"[TILING] 拼接后总尺寸: {total_width}x{total_height}")
            self.logger.info(f"[TILING] 预计内存需求: {total_width * total_height * 3 / 1024 / 1024:.1f}MB")

            # 创建大画布
            tiled_image = Image.new('RGB', (total_width, total_height))

            # 拼接背景图（重复使用同一张背景图）
            for row in range(grid_rows):
                for col in range(grid_cols):
                    x = col * tile_width
                    y = row * tile_height
                    tiled_image.paste(base_image, (x, y))

            # 生成临时文件路径
            temp_dir = tempfile.gettempdir()
            temp_filename = f"tiled_background_{uuid.uuid4().hex[:8]}.jpg"
            temp_path = os.path.join(temp_dir, temp_filename)

            # 保存拼接后的背景图
            tiled_image.save(temp_path, 'JPEG', quality=85, optimize=True)

            # 记录临时文件以便后续清理
            self._register_temp_file(temp_path)

            self.logger.info(f"[TILING] 拼接背景图已保存: {temp_path}")
            self.logger.debug(f"[TILING] 网格: {grid_rows}x{grid_cols}, 瓦片: {tile_width}x{tile_height}")

            return temp_path

        except Exception as e:
            self.logger.error(f"[TILING] 背景图拼接失败: {e}")
            # 拼接失败时返回原始背景图
            return background_path

    def _calculate_safe_scale_for_single_background(self, total_rows, cols, bg_width, bg_height, background_config, random_state):
        """
        为单个背景图计算安全的缩放倍数

        Args:
            total_rows: 表格总行数
            cols: 表格列数
            bg_width: 背景图宽度
            bg_height: 背景图高度
            background_config: 背景配置
            random_state: 随机状态

        Returns:
            安全的缩放倍数
        """
        # V5.3优化：估算表格实际尺寸（使用更保守的估算以配合3行3列基准）
        estimated_cell_width = 150  # 更保守的单元格宽度估算（从120增加到150）
        estimated_cell_height = 50  # 更保守的单元格高度估算（从40增加到50）

        estimated_table_width = cols * estimated_cell_width + 300  # 加更大边距（从200增加到300）
        estimated_table_height = total_rows * estimated_cell_height + 300  # 加更大边距

        self.logger.debug(f"[SIZE_ESTIMATION] 单元格估算: {estimated_cell_width}x{estimated_cell_height}")
        self.logger.debug(f"[SIZE_ESTIMATION] 表格估算: {estimated_table_width}x{estimated_table_height}")

        # 计算需要的最小缩放倍数
        required_scale_x = estimated_table_width / bg_width if bg_width > 0 else 1.0
        required_scale_y = estimated_table_height / bg_height if bg_height > 0 else 1.0
        min_required_scale = max(required_scale_x, required_scale_y, 1.0)

        # 添加安全边距（20%）
        safe_scale = min_required_scale * 1.2

        # V5.3优化：对于相对较小的表格，可以添加装饰性随机缩放
        # 由于现在3行3列就会触发拼接，这里的阈值也相应调整
        if total_rows <= 6 and cols <= 6 and hasattr(background_config, 'max_scale_factor'):
            decorative_scale = random_state.uniform(1.0, min(background_config.max_scale_factor, 2.5))
            final_scale = max(safe_scale, decorative_scale)
            self.logger.info(f"[SINGLE_BG_SCALE] 添加装饰性缩放: {decorative_scale:.2f}")
        else:
            final_scale = safe_scale
            self.logger.info(f"[SINGLE_BG_SCALE] 仅使用安全缩放: {safe_scale:.2f}")

        # 应用系统限制
        MAX_SINGLE_SCALE = 8.0  # 单个背景图最大缩放8倍
        final_scale = min(final_scale, MAX_SINGLE_SCALE)

        self.logger.debug(f"[SINGLE_BG_SCALE] 表格{total_rows}x{cols}, 需求缩放:{min_required_scale:.2f}, 最终缩放:{final_scale:.2f}")

        return final_scale

    def _create_scaled_background(self, background_path, target_width, target_height):
        """
        创建缩放后的背景图

        Args:
            background_path: 原始背景图路径
            target_width: 目标宽度
            target_height: 目标高度

        Returns:
            缩放后的背景图路径
        """
        from PIL import Image
        import os
        import tempfile
        import uuid

        try:
            # 加载并缩放背景图
            image = Image.open(background_path)
            scaled_image = image.resize((target_width, target_height), Image.Resampling.LANCZOS)

            # 生成临时文件路径
            temp_dir = tempfile.gettempdir()
            temp_filename = f"scaled_background_{uuid.uuid4().hex[:8]}.jpg"
            temp_path = os.path.join(temp_dir, temp_filename)

            # 保存缩放后的背景图
            scaled_image.save(temp_path, 'JPEG', quality=85, optimize=True)

            # 记录临时文件
            self._register_temp_file(temp_path)

            self.logger.debug(f"[SCALED_BG] 缩放背景图已保存: {temp_path}")

            return temp_path

        except Exception as e:
            self.logger.error(f"[SCALED_BG] 背景图缩放失败: {e}")
            return background_path

    def _register_temp_file(self, file_path):
        """
        注册临时文件以便后续清理

        Args:
            file_path: 临时文件路径
        """
        if not hasattr(self, '_temp_files'):
            self._temp_files = []
        self._temp_files.append(file_path)
        self.logger.debug(f"[TEMP_FILE] 注册临时文件: {file_path}")

    def cleanup_temp_files(self):
        """
        清理所有临时文件
        """
        if not hasattr(self, '_temp_files'):
            return

        import os
        cleaned_count = 0

        for file_path in self._temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    cleaned_count += 1
                    self.logger.debug(f"[TEMP_FILE] 已清理: {file_path}")
            except Exception as e:
                self.logger.warning(f"[TEMP_FILE] 清理失败 {file_path}: {e}")

        self._temp_files.clear()

        if cleaned_count > 0:
            self.logger.info(f"[TEMP_FILE] 已清理 {cleaned_count} 个临时背景图文件")

    def _estimate_border_width(self, style_config):
        """
        估算边框宽度
        """
        # 根据边框模式估算
        border_mode = getattr(style_config, 'border_mode', {})

        if isinstance(border_mode, dict) and 'mode_options' in border_mode:
            # 概率化边框模式，假设平均边框宽度
            return 1.5  # 平均边框宽度
        else:
            return 2  # 默认边框宽度

    def _calculate_font_width_factor(self, font_config):
        """
        计算字体宽度因子
        """
        # 不同字体族的宽度差异
        default_family = getattr(font_config, 'default_family', 'Arial')

        if isinstance(default_family, dict):
            # 概率化配置，假设平均宽度
            return 1.0
        elif isinstance(default_family, str):
            # 根据字体族调整宽度因子
            if 'Times' in default_family:
                return 0.95  # Times字体稍窄
            elif 'Courier' in default_family:
                return 1.2   # 等宽字体较宽
            elif 'Arial' in default_family or 'Helvetica' in default_family:
                return 1.0   # 标准宽度
            else:
                return 1.0   # 默认宽度
        else:
            return 1.0

    def _calculate_overflow_impact_factor(self, style_config):
        """
        计算内容溢出处理策略对高度的影响因子
        """
        overflow_strategy = getattr(style_config, 'overflow_strategy', 'wrap')

        if overflow_strategy == 'wrap':
            # wrap模式可能增加行高（内容换行）
            return 1.3  # 可能增加30%高度
        else:
            # truncate模式保持固定高度
            return 1.0

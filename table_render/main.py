"""
TableRender 命令行入口

提供命令行接口来启动表格图像生成任务。
"""

import argparse
import logging
import sys
from pathlib import Path

import yaml
from pydantic import ValidationError

from .config import RenderConfig
from .main_generator import MainGenerator


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_config(config_path: str) -> RenderConfig:
    """
    加载并验证配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        验证后的配置对象
        
    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML格式错误
        ValidationError: 配置验证失败
    """
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"配置文件未找到: {config_path}")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)
    
    return RenderConfig(**config_data)


def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 设置命令行参数解析器
    parser = argparse.ArgumentParser(
        description="TableRender: 一个可控的表格图像合成工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python -m table_render configs/v3.2_enhanced_style.yaml --num-samples 10
  python -m table_render --config configs/v3.2_enhanced_style.yaml --num-samples 10
  python -m table_render configs/v4_background_test.yaml --num-samples 3 --debug
        """
    )

    # 支持位置参数和选项参数两种方式
    parser.add_argument(
        "config_file",
        nargs="?",
        type=str,
        help="YAML配置文件的路径（位置参数）"
    )
    parser.add_argument(
        "--config",
        type=str,
        help="YAML配置文件的路径（选项参数）"
    )
    parser.add_argument(
        "--num-samples",
        type=int,
        required=True,
        help="要生成的样本数量"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式，生成每个阶段的中间图像和标注"
    )
    
    args = parser.parse_args()

    # 确定配置文件路径
    config_path = args.config_file or args.config
    if not config_path:
        parser.error("必须提供配置文件路径，可以使用位置参数或 --config 选项")

    try:
        # 加载和验证配置
        logger.info(f"正在加载配置文件: {config_path}")
        render_config = load_config(config_path)
        logger.info("配置加载并验证成功")
        
        # 初始化并运行主生成器
        if args.debug:
            logger.info(f"调试模式：开始生成 {args.num_samples} 个样本...")
            logger.info("调试模式将在每个样本的输出目录下创建 debug_sample_XXXXXX 子目录")
        else:
            logger.info(f"开始生成 {args.num_samples} 个样本...")

        generator = MainGenerator(render_config, debug_mode=args.debug)
        generator.generate(args.num_samples)

        if args.debug:
            logger.info("所有样本生成完毕（包含调试信息）")
        else:
            logger.info("所有样本生成完毕")
        
    except FileNotFoundError as e:
        logger.error(f"错误: {e}")
        sys.exit(1)
    except yaml.YAMLError as e:
        logger.error(f"错误: 配置文件YAML格式错误 - {e}")
        sys.exit(1)
    except ValidationError as e:
        logger.error(f"错误: 配置验证失败 - {e}")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"生成过程中发生严重错误: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()

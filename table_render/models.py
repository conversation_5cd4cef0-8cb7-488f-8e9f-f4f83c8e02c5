"""
内部数据模型

定义表格的内部表示，与具体的渲染技术解耦。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional


@dataclass
class CellBorderStyle:
    """
    单元格边框样式

    直接存储每个单元格四条边的边框状态，简化边框处理逻辑。
    V4.5新增：支持每条边的颜色信息。
    """
    top: int = 0
    right: int = 0
    bottom: int = 0
    left: int = 0

    # V4.5新增：边框颜色信息
    top_color: str = "#000000"
    right_color: str = "#000000"
    bottom_color: str = "#000000"
    left_color: str = "#000000"

    def __str__(self) -> str:
        return f"Border(t={self.top}, r={self.right}, b={self.bottom}, l={self.left}, colors=t:{self.top_color}, r:{self.right_color}, b:{self.bottom_color}, l:{self.left_color})"


@dataclass
class BorderDecisions:
    """
    边框决策数据模型

    在表格结构构建阶段确定所有边框的显示状态，后续组件基于此决策生成CSS和HTML。
    """
    # 行边框决策：row_index -> bool (该行底部是否显示边框)
    row_borders: Dict[int, bool] = field(default_factory=dict)
    # 列边框决策：col_index -> bool (该列右侧是否显示边框)
    col_borders: Dict[int, bool] = field(default_factory=dict)
    # 外框边框
    outer_frame: bool = False
    # 表头分割线
    header_separator: bool = False

    def __str__(self) -> str:
        return f"BorderDecisions(rows={len(self.row_borders)}, cols={len(self.col_borders)}, outer={self.outer_frame}, header_sep={self.header_separator})"


@dataclass
class CellModel:
    """
    单元格数据模型

    代表表格中的一个单元格，包含其位置、内容和属性信息。
    新增：直接存储边框样式，简化边框处理流程。
    """
    cell_id: str
    row_index: int
    col_index: int
    row_span: int = 1
    col_span: int = 1
    content: str = ""
    is_header: bool = False
    border_style: CellBorderStyle = field(default_factory=CellBorderStyle)
    
    def __str__(self) -> str:
        return f"Cell({self.cell_id}, content='{self.content}')"


@dataclass
class RowModel:
    """
    行数据模型
    
    代表表格中的一行，包含该行的所有单元格。
    """
    cells: List[CellModel] = field(default_factory=list)
    row_index: int = -1
    
    def __str__(self) -> str:
        return f"Row({self.row_index}, cells={len(self.cells)})"


@dataclass
class CSVSamplingMetadata:
    """
    CSV采样元数据

    V5.0新增：记录CSV内容采样的详细信息，用于调试和追踪。
    """
    source_file: str = ""
    selected_columns: List[int] = field(default_factory=list)
    selected_rows: List[int] = field(default_factory=list)
    csv_total_columns: int = 0
    csv_total_rows: int = 0
    sampling_mode: str = "random"


@dataclass
class TableModel:
    """
    表格数据模型

    V3.1版本：实现表头/主体分离，支持<thead>和<tbody>的结构性分离。
    新增：边框决策信息，在结构构建阶段确定所有边框状态。
    V5.0新增：CSV采样元数据，用于记录内容来源信息。
    """
    header_rows: List[RowModel] = field(default_factory=list)
    body_rows: List[RowModel] = field(default_factory=list)
    border_decisions: BorderDecisions = field(default_factory=BorderDecisions)

    # V4.5新增：颜色差异边框映射，用于标注生成
    color_diff_borders: Optional[Dict[str, Dict[str, bool]]] = field(default=None)

    # 新增：边框模式信息，用于标注生成时确定type值
    border_mode: Optional[str] = field(default=None)

    # 新增：表头分割线位置标记，用于智能表头分割线处理
    header_separator_positions: Optional[set] = field(default=None)

    # V5.0新增：CSV采样元数据
    csv_sampling_metadata: Optional[CSVSamplingMetadata] = field(default=None)

    # 向后兼容属性：保持与V3版本的兼容性
    @property
    def rows(self) -> List[RowModel]:
        """
        向后兼容属性：返回所有行（表头+主体）

        注意：这是为了保持与V3版本的兼容性，新代码应该使用header_rows和body_rows
        """
        return self.header_rows + self.body_rows

    @property
    def num_rows(self) -> int:
        """获取表格的总行数"""
        return len(self.header_rows) + len(self.body_rows)

    @property
    def num_header_rows(self) -> int:
        """获取表头行数"""
        return len(self.header_rows)

    @property
    def num_body_rows(self) -> int:
        """获取主体行数"""
        return len(self.body_rows)

    @property
    def num_cols(self) -> int:
        """
        获取表格的总列数

        优先基于表头行计算，如果没有表头则基于主体第一行。
        """
        if self.header_rows:
            # 基于第一个表头行计算列数
            return sum(cell.col_span for cell in self.header_rows[0].cells)
        elif self.body_rows:
            # 基于第一个主体行计算列数
            return sum(cell.col_span for cell in self.body_rows[0].cells)
        else:
            return 0

    def __str__(self) -> str:
        return f"Table({self.num_rows}x{self.num_cols}, header={len(self.header_rows)}, body={len(self.body_rows)})"

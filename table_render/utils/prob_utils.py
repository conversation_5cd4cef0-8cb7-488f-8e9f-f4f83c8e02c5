"""
概率处理工具模块

提供概率计算和随机选择的通用函数，支持TableRender v3.4的概率化配置机制。
"""

import random
from typing import List, Any, Tuple, Union


def normalize_probabilities(probabilities: List[float]) -> List[float]:
    """
    将概率列表归一化，使其总和为1
    
    Args:
        probabilities: 概率列表
        
    Returns:
        归一化后的概率列表
        
    Raises:
        ValueError: 如果概率列表为空或所有概率都为0
    """
    if not probabilities:
        raise ValueError("概率列表不能为空")
    
    total = sum(probabilities)
    if total == 0:
        raise ValueError("所有概率不能都为0")
    
    return [p / total for p in probabilities]


def choose_from_list(
    options: List[Any], 
    probabilities: List[float] = None, 
    rng: random.Random = None
) -> Any:
    """
    根据给定的概率从列表中选择一个选项
    
    Args:
        options: 选项列表
        probabilities: 概率列表，如果为None则等概率选择
        rng: 随机数生成器，如果为None则使用默认的random模块
        
    Returns:
        选中的选项
        
    Raises:
        ValueError: 如果选项列表为空或概率列表长度与选项列表不匹配
    """
    if not options:
        raise ValueError("选项列表不能为空")
    
    if rng is None:
        rng = random
    
    # 如果没有提供概率，等概率选择
    if probabilities is None:
        return rng.choice(options)
    
    # 检查概率列表长度
    if len(probabilities) != len(options):
        raise ValueError(f"概率列表长度({len(probabilities)})与选项列表长度({len(options)})不匹配")
    
    # 归一化概率
    normalized_probs = normalize_probabilities(probabilities)
    
    # 使用累积概率进行选择
    cumulative_prob = 0.0
    rand_value = rng.random()
    
    for i, prob in enumerate(normalized_probs):
        cumulative_prob += prob
        if rand_value <= cumulative_prob:
            return options[i]
    
    # 由于浮点数精度问题，可能会到达这里，返回最后一个选项
    return options[-1]


def get_from_range(
    value_range: Union[List[int], List[float]], 
    rng: random.Random = None
) -> Union[int, float]:
    """
    从一个数值范围 [min, max] 中随机选择一个值（均匀分布）
    
    Args:
        value_range: 数值范围，格式为 [min, max]
        rng: 随机数生成器，如果为None则使用默认的random模块
        
    Returns:
        范围内的随机值
        
    Raises:
        ValueError: 如果范围格式不正确
    """
    if not isinstance(value_range, list) or len(value_range) != 2:
        raise ValueError("数值范围必须是包含两个元素的列表 [min, max]")
    
    min_val, max_val = value_range
    
    if min_val > max_val:
        raise ValueError(f"最小值({min_val})不能大于最大值({max_val})")
    
    if rng is None:
        rng = random
    
    # 根据数据类型选择合适的随机函数
    if isinstance(min_val, int) and isinstance(max_val, int):
        # 整数范围 - 处理min_val == max_val的情况
        if min_val == max_val:
            return min_val
        else:
            return rng.randint(min_val, max_val + 1)  # numpy.randint的high是exclusive，所以需要+1
    else:
        # 浮点数范围
        if min_val == max_val:
            return float(min_val)
        else:
            return rng.uniform(float(min_val), float(max_val))


def choose_weighted_option(
    options_with_weights: List[Tuple[Any, float]], 
    rng: random.Random = None
) -> Any:
    """
    从带权重的选项列表中选择一个选项
    
    Args:
        options_with_weights: 选项和权重的元组列表，格式为 [(option1, weight1), (option2, weight2), ...]
        rng: 随机数生成器，如果为None则使用默认的random模块
        
    Returns:
        选中的选项
        
    Raises:
        ValueError: 如果选项列表为空
    """
    if not options_with_weights:
        raise ValueError("选项列表不能为空")
    
    options = [item[0] for item in options_with_weights]
    weights = [item[1] for item in options_with_weights]
    
    return choose_from_list(options, weights, rng)


def resolve_probabilistic_value(
    value: Union[Any, List[Any], List[Tuple[Any, float]]], 
    rng: random.Random = None
) -> Any:
    """
    解析概率化的值
    
    支持以下格式：
    1. 单个值：直接返回
    2. 值列表：等概率选择
    3. (值, 权重)元组列表：按权重选择
    
    Args:
        value: 要解析的值
        rng: 随机数生成器，如果为None则使用默认的random模块
        
    Returns:
        解析后的具体值
    """
    if rng is None:
        rng = random
    
    # 单个值，直接返回
    if not isinstance(value, list):
        return value
    
    # 空列表
    if not value:
        raise ValueError("值列表不能为空")
    
    # 检查是否为(值, 权重)元组列表
    if all(isinstance(item, (tuple, list)) and len(item) == 2 for item in value):
        return choose_weighted_option(value, rng)
    
    # 普通值列表，等概率选择
    return choose_from_list(value, rng=rng)

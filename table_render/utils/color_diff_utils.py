"""
颜色差异工具

V4.5新增：专门处理相邻单元格颜色差异检测和边框标注调整的工具类。
"""

import logging
from typing import Dict, Tuple, List, Optional

from .color_utils import ColorManager


class ColorDifferenceDetector:
    """
    颜色差异检测器
    
    负责检测相邻单元格的颜色差异，并根据差异调整边框标注。
    """
    
    def __init__(self, color_manager: ColorManager):
        """
        初始化颜色差异检测器
        
        Args:
            color_manager: 颜色管理器实例
        """
        self.color_manager = color_manager
        self.logger = logging.getLogger(__name__)

    def detect_color_based_borders_with_actual_colors(self, table_model, actual_colors):
        """
        V4.5修正：使用实际颜色进行颜色差异边框检测，生成独立的差异映射
        智能表头分割线：对表头分割线位置进行特殊处理

        Args:
            table_model: 表格模型
            actual_colors: 实际的单元格颜色映射 {cell_id: background_color}

        Returns:
            颜色差异边框映射 {cell_id: {'top': bool, 'right': bool, 'bottom': bool, 'left': bool}}
        """
        self.logger.debug("开始基于实际颜色检测颜色差异边框")

        # 创建单元格位置映射，便于查找相邻单元格
        cell_map = self._create_cell_position_map(table_model)

        # 颜色差异边框映射
        color_diff_borders = {}

        # 为每个单元格检测颜色差异
        for row in table_model.rows:
            for cell in row.cells:
                color_diff_borders[cell.cell_id] = self._detect_cell_color_borders_with_actual_colors(
                    cell, cell_map, actual_colors, table_model
                )

        self.logger.debug("基于实际颜色的颜色差异边框检测完成")
        return color_diff_borders

    def _detect_cell_color_borders_with_actual_colors(self, cell, cell_map, actual_colors, table_model):
        """
        V4.5修正：使用实际颜色检测单个单元格的颜色差异边框，返回差异结果
        智能表头分割线：对表头分割线位置进行特殊的颜色差异检测

        Args:
            cell: 单元格对象
            cell_map: 单元格位置映射
            actual_colors: 实际的单元格颜色映射
            table_model: 表格模型（用于获取表头分割线位置信息）

        Returns:
            颜色差异边框字典 {'top': bool, 'right': bool, 'bottom': bool, 'left': bool}
        """
        # 获取单元格的实际背景色
        cell_bg_color = actual_colors.get(cell.cell_id, '#FFFFFF')

        # 初始化颜色差异边框结果
        color_diff_result = {
            'top': False,
            'right': False,
            'bottom': False,
            'left': False
        }

        # 获取表头分割线位置信息
        header_separator_positions = getattr(table_model, 'header_separator_positions', set())

        # 检查上边框
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'top')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            if self._should_force_border(cell_bg_color, adjacent_bg_color):
                color_diff_result['top'] = True

        # 检查右边框
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'right')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            if self._should_force_border(cell_bg_color, adjacent_bg_color):
                color_diff_result['right'] = True

        # 检查下边框（智能表头分割线处理）
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'bottom')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            # 检查是否是表头分割线位置
            is_header_separator = (cell.row_index, cell.col_index, 'bottom') in header_separator_positions

            if is_header_separator:
                # 对于表头分割线，基于颜色差异智能决定是否显示
                if self._should_force_border(cell_bg_color, adjacent_bg_color):
                    color_diff_result['bottom'] = True
                    self.logger.debug(f"表头分割线位置 ({cell.row_index}, {cell.col_index}) 颜色差异显著，显示边框")
                else:
                    color_diff_result['bottom'] = False
                    self.logger.debug(f"表头分割线位置 ({cell.row_index}, {cell.col_index}) 颜色相似，不显示边框")
            else:
                # 非表头分割线位置，按正常逻辑处理
                if self._should_force_border(cell_bg_color, adjacent_bg_color):
                    color_diff_result['bottom'] = True

        # 检查左边框
        adjacent_cell = self._get_adjacent_cell(cell, cell_map, 'left')
        if adjacent_cell:
            adjacent_bg_color = actual_colors.get(adjacent_cell.cell_id, '#FFFFFF')
            if self._should_force_border(cell_bg_color, adjacent_bg_color):
                color_diff_result['left'] = True

        return color_diff_result
    
    def _create_cell_position_map(self, table_model) -> Dict[Tuple[int, int], object]:
        """
        创建单元格位置映射
        
        为合并单元格的每个覆盖位置都建立映射，便于查找相邻单元格。
        
        Args:
            table_model: 表格模型
            
        Returns:
            位置到单元格的映射字典
        """
        cell_map = {}
        for row in table_model.rows:
            for cell in row.cells:
                # 为合并单元格的每个覆盖位置都建立映射
                for r in range(cell.row_index, cell.row_index + cell.row_span):
                    for col in range(cell.col_index, cell.col_index + cell.col_span):
                        cell_map[(r, col)] = cell
        return cell_map

    def _get_adjacent_cell(self, cell, cell_map, direction):
        """
        获取指定方向的相邻单元格
        
        Args:
            cell: 当前单元格
            cell_map: 单元格位置映射
            direction: 方向 ('top', 'right', 'bottom', 'left')
            
        Returns:
            相邻单元格，如果没有则返回None
        """
        # 根据方向确定相邻位置
        if direction == 'top':
            adjacent_row = cell.row_index - 1
            adjacent_col = cell.col_index
        elif direction == 'bottom':
            adjacent_row = cell.row_index + cell.row_span
            adjacent_col = cell.col_index
        elif direction == 'left':
            adjacent_row = cell.row_index
            adjacent_col = cell.col_index - 1
        elif direction == 'right':
            adjacent_row = cell.row_index
            adjacent_col = cell.col_index + cell.col_span
        else:
            return None
        
        # 查找相邻单元格
        return cell_map.get((adjacent_row, adjacent_col))
    
    def _should_force_border(self, color1: str, color2: str) -> bool:
        """
        判断是否应该强制显示边框
        
        根据两个颜色的差异决定是否需要边框。
        
        Args:
            color1: 第一个颜色
            color2: 第二个颜色
            
        Returns:
            如果需要边框返回True，否则返回False
        """
        # 使用RGB差值和判断颜色差异
        return not self.color_manager.are_colors_similar(color1, color2)
# TableRender V4.5 边框颜色功能测试配置
# 用于验证新增的 randomize_border_color_probability 配置项和颜色差异驱动的边框标注

# 输出配置
output:
  output_dir: "./output/v4.5_test"

# 表格结构配置
structure:
  body_rows: 3
  cols: 4
  header_rows: 1
  merge_probability: 0.2
  max_row_span: 2
  max_col_span: 2

# 内容配置
content:
  source_type: "programmatic"
  programmatic_types: ["date", "currency", "percentage", "text"]

# 样式配置
style:
  overflow_strategy: "wrap"

  # 公共样式配置
  common:
    # 字体配置
    font:
      font_dirs: "./assets/fonts/"
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.2
      italic_probability: 0.1
      fallback_font: "Microsoft YaHei"

    # 对齐方式
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]

    # 内边距
    padding: 8

    # V3.3颜色随机化概率：使用彩色单元格以便测试边框颜色对比度
    randomize_color_probability: 0.8

    # V4.5新特性：线条颜色随机化概率 - 测试用高概率
    randomize_border_color_probability: 0.8

    # 合并单元格居中概率
    merged_cell_center_probability: 0.6

    # 颜色对比度配置
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7

  # 样式继承配置
  inheritance:
    font_family_change_probability: 0.2
    font_size_change_probability: 0.3
    alignment_change_probability: 0.4
    padding_change_probability: 0.3
    text_color_change_probability: 0.3
    background_color_change_probability: 0.2

  # V4.5边框模式配置 - 使用概率化边框模式以便测试不同场景下的边框颜色效果
  border_mode:
    mode_options:
      # 30%概率使用完整边框：测试所有边框的颜色效果
      - probability: 0.0
        config:
          mode: "full"

      # 20%概率使用无边框：测试颜色差异驱动的边框标注
      - probability: 1.0
        config:
          mode: "none"

      # 50%概率使用半边框模式：测试部分边框的颜色效果
      - probability: 0.0
        config:
          mode: "semi"
          semi_config:
            row_line_probability: 0.7       # 70%概率显示行线
            col_line_probability: 0.6       # 60%概率显示列线
            outer_frame: true                # 保留外框
            header_separator: true           # 保留表头分割线

  # 斑马条纹 - 适度启用以增加颜色多样性
  zebra_stripes: 0.4

  # 尺寸配置
  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# V4.5测试：简化的后处理配置，专注于边框颜色测试
postprocessing:
  # 启用透明度以便观察边框颜色效果
  table_blending:
    enable_transparency: false              # 关闭透明度，确保颜色清晰可见

  # 关闭其他后处理效果，专注于边框颜色测试
  perspective:
    probability: 0.0

  background:
    background_dirs: []

  blur:
    probability: 0.0

  noise:
    probability: 0.0

# 种子配置
seed: 42

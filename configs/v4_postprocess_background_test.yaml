# TableRender V4.0 传统图像后处理背景图测试配置
# 使用传统的图像后处理模式，用于对比CSS渲染模式的效果

output:
  output_dir: "./output/"

structure:
  body_rows: 3
  cols: 4
  header_rows: 1
  merge_probability: 0.1
  max_row_span: 2
  max_col_span: 2

content:
  source_type: "programmatic"
  programmatic_types: ["date", "currency", "percentage", "text"]

style:
  overflow_strategy: "wrap"
  
  common:
    font:
      font_dirs: "./assets/fonts/"
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.1
      italic_probability: 0.1
      fallback_font: "Microsoft YaHei"
    
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: 8
    randomize_color_probability: 0.3
    merged_cell_center_probability: 0.5
    
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7
  
  inheritance:
    font_family_change_probability: 0.2
    font_size_change_probability: 0.3
    alignment_change_probability: 0.4
    padding_change_probability: 0.3
    text_color_change_probability: 0.3
    background_color_change_probability: 0.2
  
  border_mode:
    mode: "full"
  
  zebra_stripes: 0.3
  
  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# V4.0：传统图像后处理模式
postprocessing:
  # 透视变换（图像后处理）
  perspective:
    probability: 0
    max_offset_ratio: 0.03

  # 背景图合成配置
  background:
    background_dirs: ["./assets/backgrounds/"]
    background_dir_probabilities: [1.0]
    max_scale_factor: 2.0

  # 可选的图像后处理（模糊、噪声）
  blur:
    probability: 0
    radius_range: [1.0, 2.0]

  noise:
    probability: 0
    intensity_range: [5, 10]

  # V4.5新增：降质效果配置
  degradation_blur:
    probability: 0.5              # 模糊效果（高斯/运动/均值模糊随机选择）
  degradation_noise:
    probability: 0.5              # 高斯噪声
  degradation_fade_global:
    probability: 0.2              # 全局褪色
  degradation_fade_local:
    probability: 1              # 局部褪色
  degradation_uneven_lighting:
    probability: 0.2              # 不均匀光照
  degradation_jpeg:
    probability: 0.2              # JPEG压缩
  degradation_darker_brighter:
    probability: 0.2              # 亮度/对比度调整
  degradation_gamma_correction:
    probability: 0.2              # 伽马校正

seed: 42

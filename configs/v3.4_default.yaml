# TableRender v3.4 默认配置文件
# 展示概率化配置机制的完整功能
#
# 概率化配置说明：
# - ProbabilisticRange: 支持多个数值范围及其概率分布
# - ProbabilisticOptions: 支持离散选项及其概率分布
# - 所有概率值会自动归一化，无需手动确保总和为1
# - 设计原则：多样性优先，确保生成的表格具有丰富的变化

# 输出配置
output:
  output_dir: "./output/"
  label_suffix: "_table_annotation"  # 标注文件后缀，用于区分图像和标注文件

# 表格结构配置 - 全面使用概率化配置
structure:
  # 概率化表体行数：偏向小表格以提高生成效率，但保持多样性
  # 70%概率生成3-5行的小表格，30%概率生成6-8行的中等表格
  body_rows:
    range_list: [[3, 5], [6, 8]]
    probability_list: [0.7, 0.3]

  # 概率化列数：平衡简单和复杂表格的比例
  # 50%概率生成3-4列的简单表格，50%概率生成5-6列的复杂表格
  cols:
    range_list: [[3, 4], [5, 6]]
    probability_list: [0.5, 0.5]

  # 概率化表头行数：大部分表格使用单行表头，少数使用多行表头
  # 80%概率单行表头，20%概率双行表头（适合复杂表格）
  header_rows:
    range_list: [[1, 3], [2, 4]]
    probability_list: [0.8, 0.2]
  
  # 概率化合并概率：控制单元格合并的频率
  # 60%概率使用低合并率(0.1-0.2)，保持表格结构清晰
  # 40%概率使用中等合并率(0.3-0.4)，增加表格复杂度
  merge_probability:
    range_list: [[0.1, 0.2], [0.3, 0.4]]
    probability_list: [0.6, 0.4]

  # 概率化最大行跨度：控制垂直合并的最大范围
  # 60%概率限制为2行跨度（常见情况）
  # 30%概率允许3行跨度（中等复杂度）
  # 10%概率允许4行跨度（高复杂度，用于特殊表格）
  max_row_span:
    option_list: [2, 3, 4]
    probability_list: [0.6, 0.3, 0.1]

  # 概率化最大列跨度：控制水平合并的最大范围
  # 70%概率限制为2列跨度（保持可读性）
  # 25%概率允许3列跨度（适度复杂）
  # 5%概率允许4列跨度（高复杂度，罕见情况）
  max_col_span:
    option_list: [2, 3, 4]
    probability_list: [0.7, 0.25, 0.05]

# 内容配置 - 程序化生成多样化数据
content:
  source_type: "programmatic"  # 使用程序化生成，确保数据的多样性和一致性
  # 包含多种数据类型以模拟真实表格场景
  programmatic_types: ["date", "currency", "percentage", "text"]

# 样式配置 - 全面的概率化样式系统
style:
  # 公共样式配置：作为表头和表体样式的基础
  common:
    # 字体配置 - 支持概率化字体大小和字体目录选择
    font:
      # 概率化字体目录：60%概率中文字体，30%概率英文字体，10%概率装饰字体
      font_dirs: ["./assets/fonts/chinese/", "./assets/fonts/english/", "./assets/fonts/decorative/"]
      font_dir_probabilities: [0.6, 0.3, 0.1]

      # 概率化字体族：50%概率Arial，30%概率Times New Roman，20%概率Helvetica
      default_family:
        option_list: ["Arial", "Times New Roman", "Helvetica"]
        probability_list: [0.5, 0.3, 0.2]

      # 概率化字体大小：60%概率12-14px，40%概率15-18px
      default_size:
        range_list: [[12, 14], [15, 18]]
        probability_list: [0.6, 0.4]

      bold_probability: 0.1
      italic_probability: 0.05
      fallback_font: "Microsoft YaHei"
    
    # 概率化水平对齐：模拟真实文档中的对齐偏好
    # 50%概率左对齐（最常见，适合文本内容）
    # 30%概率居中对齐（适合标题和数值）
    # 20%概率右对齐（适合数值和金额）
    horizontal_align:
      option_list: ["left", "center", "right"]
      probability_list: [0.5, 0.3, 0.2]

    # 概率化垂直对齐：平衡美观性和实用性
    # 40%概率顶部对齐（传统表格样式）
    # 40%概率中间对齐（现代表格样式）
    # 20%概率底部对齐（特殊设计需求）
    vertical_align:
      option_list: ["top", "middle", "bottom"]
      probability_list: [0.4, 0.4, 0.2]

    # 概率化内边距：在紧凑性和可读性之间平衡
    # 70%概率使用较小内边距(4-6px)，保持紧凑布局
    # 30%概率使用较大内边距(7-10px)，提高可读性
    padding:
      range_list: [[4, 6], [7, 10]]
      probability_list: [0.7, 0.3]
    
    # 颜色随机化概率：控制是否使用随机颜色而非默认黑白配色
    randomize_color_probability: 0.4  # 40%概率使用彩色，60%概率使用默认配色

    # 合并单元格居中概率：优化合并单元格的视觉效果
    merged_cell_center_probability: 0.6  # 60%概率将合并单元格设为居中对齐

    # 颜色对比度配置：确保生成的颜色组合具有良好的可读性
    color_contrast:
      min_contrast_ratio: 4.5  # 符合WCAG AA标准的最小对比度
      use_soft_colors_probability: 0.2  # 80%概率使用柔和颜色，提升视觉舒适度

  # 样式继承配置：控制表体样式相对于表头样式的变化概率
  inheritance:
    # 字体族变化概率：20%概率表体使用与表头不同的字体
    font_family_change_probability: 0.2
    # 字体大小变化概率：30%概率表体使用与表头不同的字体大小
    font_size_change_probability: 0.3
    # 对齐方式变化概率：40%概率表体使用与表头不同的对齐方式
    alignment_change_probability: 0.4
    # 内边距变化概率：30%概率表体使用与表头不同的内边距
    padding_change_probability: 0.3
    # 文本颜色变化概率：30%概率表体使用与表头不同的文本颜色
    text_color_change_probability: 0.3
    # 背景颜色变化概率：20%概率表体使用与表头不同的背景颜色
    background_color_change_probability: 0.2

  # 概率化边框模式配置：提供多样化的表格边框样式
  border_mode:
    mode_options:
      # 40%概率使用完整边框：传统表格样式，结构清晰
      - probability: 0.4
        config:
          mode: "full"

      # 30%概率使用无边框：现代简洁样式，适合报告和演示
      - probability: 0.3
        config:
          mode: "none"

      # 30%概率使用半边框模式：部分边框，平衡美观性和结构性
      - probability: 0.3
        config:
          mode: "semi"
          semi_config:
            row_line_probability: 0.6  # 60%概率显示行线
            col_line_probability: 0.5  # 50%概率显示列线
            outer_frame: true          # 保留外框以维持表格边界
            header_separator: true     # 保留表头分割线以突出表头

  # 斑马条纹概率：30%概率启用斑马条纹，增加表格的视觉层次
  zebra_stripes: 0.3

  # 尺寸配置：V3.4新增可变行高列宽功能
  sizing:
    # 全局默认配置
    default_row_height: "auto"  # 默认行高自适应内容
    default_col_width: "auto"   # 默认列宽自适应内容

    # 行级差异化配置：模拟真实文档中的行高变化
    row_configs:
      # 配置1：表头行强调 - 40%概率启用
      - name: "header_emphasis"
        probability: 0.0
        type: "specific"
        target_rows: [0]  # 第一行（表头）
        height_range: [50, 60]  # 表头行使用较大高度

      # 配置2：汇总行强调 - 30%概率启用
      - name: "summary_emphasis"
        probability: 1.0
        type: "specific"
        target_rows: [-1]  # 最后一行（可能是汇总行）
        height_range: [70, 80]  # 汇总行使用中等高度

      # 配置3：随机行高变化 - 25%概率启用
      - name: "random_variations"
        probability: 0.25
        type: "probabilistic"
        per_row_probability: 0.15  # 每行15%概率被选中
        height_range: [40, 50]  # 随机选中的行使用较大高度

    # 列级差异化配置：模拟发票等文档的列宽变化
#    col_configs:
#      # 配置1：标签列加宽 - 50%概率启用
#      - name: "wide_label_col"
#        probability: 0.5
#        type: "specific"
#        target_cols: [0]  # 第一列（通常是标签列）
#        width_range: [120, 150]  # 标签列使用较大宽度
#
#      # 配置2：金额列适中 - 40%概率启用
#      - name: "amount_col"
#        probability: 0.4
#        type: "specific"
#        target_cols: [-1]  # 最后一列（通常是金额列）
#        width_range: [90, 120]  # 金额列使用中等宽度
#
#      # 配置3：随机列宽变化 - 20%概率启用
#      - name: "random_narrow_cols"
#        probability: 0.2
#        type: "probabilistic"
#        per_col_probability: 0.2  # 每列20%概率被选中
#        width_range: [60, 80]  # 随机选中的列使用较小宽度

# 随机种子：确保结果可复现，便于调试和验证
seed: 42

# TableRender V4.5 OpenCV透视变换测试配置
# 统一使用OpenCV进行透视变换，确保坐标变换的精确性

output:
  output_dir: "./output/"

structure:
  body_rows: 3
  cols: 4
  header_rows: 1
  merge_probability: 0.1
  max_row_span: 2
  max_col_span: 2

content:
  source_type: "programmatic"
  programmatic_types: ["date", "currency", "percentage", "text"]

style:
  overflow_strategy: "wrap"
  
  common:
    font:
      font_dirs: "./assets/fonts/"
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.1
      italic_probability: 0.1
      fallback_font: "Microsoft YaHei"
    
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: 8
    randomize_color_probability: 0.3
    merged_cell_center_probability: 0.5
    
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7
  
  inheritance:
    font_family_change_probability: 0.2
    font_size_change_probability: 0.3
    alignment_change_probability: 0.4
    padding_change_probability: 0.3
    text_color_change_probability: 0.3
    background_color_change_probability: 0.2
  
  border_mode:
    mode: "full"
  
  zebra_stripes: 0.3
  
  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# V4.5修改：统一使用OpenCV透视变换
postprocessing:
  # 透视变换（统一使用OpenCV实现，确保坐标变换精确性）
  perspective:
    probability: 1.0
    max_offset_ratio: 0.05
  
  # 背景图合成配置
  background:
    background_dirs: ["/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper/"]
    background_dir_probabilities: [1.0]
    max_scale_factor: 2.0

    # V4.2新增：概率化边距控制（在透视变换后执行）
    margin_control:
      # 边距范围列表：定义不同的边距选项
      range_list:
        - [60, 200]    # 紧凑边距：适合密集布局

      # 对应的概率分布
      probability_list: [1.0]
  
  # 可选的图像后处理（模糊、噪声）
  blur:
    probability: 0.0
    radius_range: [1.0, 2.0]
  
  noise:
    probability: 0.0
    intensity_range: [5, 10]

seed: 42

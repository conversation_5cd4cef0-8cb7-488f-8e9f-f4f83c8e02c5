# TableRender V4.0 CSS背景图渲染测试配置
# 使用CSS直接渲染背景图和透视变换，避免坐标变换误差

output:
  output_dir: "./output/"

structure:
  body_rows: 3
  cols: 4
  header_rows: 1
  merge_probability: 0.1
  max_row_span: 2
  max_col_span: 2

content:
  source_type: "csv"
  csv_source:
    # V5.0新增：概率化目录选择（推荐）
    csv_dirs:
      - "assets/corpus/nl2sql_val/"
    csv_dir_probabilities: [1.0]  # 单目录时概率为1.0

    # 多目录配置示例：
    # csv_dirs:
    #   - "sample_data/processed_csv/"
    #   - "sample_data/external_csv/"
    #   - "sample_data/special_csv/"
    # csv_dir_probabilities: [0.5, 0.3, 0.2]  # 对应目录的选择概率

    # V5.0新增：随机采样模式（行列对应）
    sampling_mode: "random"

    # V5.0新增：空白控制配置
    blank_control:
      trigger_probability: 0.2      # 20%概率某行/列被标记为可空白
      cell_blank_probability: 0.3   # 在标记的行/列中，30%概率单元格真的为空

    # 空白控制配置示例（可选）：
    # blank_control:
    #   trigger_probability: 0.0    # 设为0禁用空白控制
    #   cell_blank_probability: 0.5

    # 基本配置
    encoding: "utf-8"

    # 单文件模式示例（向后兼容）
    # file_path: "sample_data/processed_csv/4rows_batch_001.csv"

  # 程序化生成模式示例（备用）
  # source_type: "programmatic"
  # programmatic_types: ["date", "currency", "percentage", "text"]

style:
  overflow_strategy: "wrap"
  
  common:
    font:
      font_dirs: "./assets/fonts/"
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.1
      italic_probability: 0.1
      fallback_font: "Microsoft YaHei"
    
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: 8
    randomize_color_probability: 0.3
    merged_cell_center_probability: 0.5
    
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7
  
  inheritance:
    font_family_change_probability: 0.2
    font_size_change_probability: 0.3
    alignment_change_probability: 0.4
    padding_change_probability: 0.3
    text_color_change_probability: 0.3
    background_color_change_probability: 0.2
  
  border_mode:
    mode: "full"
  
  zebra_stripes: 0.3
  
  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# V4.0新增：CSS背景图渲染模式
postprocessing:
  table_blending:
    enable_transparency: true                    # 启用表格透明度
    default_color_transparency: 0.1              # 默认色（白色/浅色）的透明度
    meaningful_color_transparency: 0.4           # 有意义颜色的透明度

  # 透视变换（V4.4.2增强：支持黑边智能检测和移除）
  perspective:
    probability: 0.8
    # 透视变换强度范围配置（类似margin_control）
    range_list:
      - [0.005, 0.015]  # 轻微：模拟正常拍摄偏差
      - [0.015, 0.035]  # 中等：模拟手机拍摄角度
      - [0.035, 0.055]  # 强烈：模拟极端拍摄条件
    probability_list: [0.5, 0.3, 0.2]  # 偏向轻微变换
    content_area_shrink_ratio: 0.1  # 内容区域缩小比例，用于更保守地避免黑边
  
  # 背景图合成配置
  background:
    background_dirs: [
      "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper/",
      "/aipdf-mlp/jiacheng/code/tsr/TableRender/assets/transparent_ink_bgs/"
    ]
    background_dir_probabilities: [0.1, 0.9]
    max_scale_factor: 5.0
    prefer_center_probability: 0.8  # 偏向中心的概率（0-1）

    # V4.2新增：概率化边距控制（V4.4.2修改：margin_control优先）
    # 优先级：表格完整性 > margin_control配置（强制执行） > 黑边移除（尽量执行）
    margin_control:
      # 边距范围列表：定义不同的边距选项
      range_list:
        - [60, 120]    # 紧凑边距：适合密集布局
#        - [20, 40]    # 较紧凑边距：平衡紧凑性和可读性
#        - [40, 80]    # 中等边距：标准文档样式
#        - [60, 120]   # 宽松边距：强调表格重要性
#        - [100, 200]  # 很宽松边距：海报或展示用途

      # 对应的概率分布：偏向中等边距，保持多样性
      probability_list: [1.0]
#      probability_list: [0.1, 0.2, 0.3, 0.25, 0.15]
  
  # 可选的图像后处理（模糊、噪声）
  blur:
    probability: 0.0
    radius_range: [1.0, 2.0]
  
  noise:
    probability: 0.0              # V4.5修改：避免与degradation_noise重复
    intensity_range: [5, 10]

  # V4.5新增：降质效果配置（CSS模式）
  # 处理顺序：CSS渲染 → 透视变换 → 背景合成 → 降质效果
  # 具体降质配置参数转 D:\project\tablerender\third_parties\doc_degradation\configs\config.py
  degradation_blur:
    probability: 0.1              # 模糊效果（高斯/运动/均值模糊随机选择）
  degradation_noise:
    probability: 0.1              # 高斯噪声
  degradation_fade_global:
    probability: 0.1              # 全局褪色
  degradation_fade_local:
    probability: 0.1                # 局部褪色
  degradation_uneven_lighting:
    probability: 0.1             # 不均匀光照
  degradation_jpeg:
    probability: 0.1              # JPEG压缩
  degradation_darker_brighter:
    probability: 0.1              # 亮度/对比度调整
  degradation_gamma_correction:
    probability: 0.1              # 伽马校正

seed: 42

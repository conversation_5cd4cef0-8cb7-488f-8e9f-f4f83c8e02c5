# Original <EMAIL>

from enum import Enum, auto
from dataclasses import dataclass
from ..configs.config import DegradationConfig
from typing import Dict, Any, Optional

from enum import Enum


class DegradationType(Enum):
    """降质类型枚举"""
    BLUR                = "blur"
    NOISE               = "noise"
    JPEG                = "jpeg"
    FADE_GLOBAL         = "fade_global"
    FADE_LOCAL          = "fade_local"
    LINE_BROKEN         = "line_broken"
    UNEVEN_LIGHTING     = "uneven_lighting"
    ARTISTIC_INK        = "artistic_ink"
    SINC                = "sinc"
    USM_SHARPEN         = "usm_sharpen"
    RANDOM_RESIZE       = "random_resize"
    ESRGAN_BLUR         = "esrgan_blur"
    DARKER_BRIGHTER     = "darker_brighter"
    GAMMA_CORRECTION    = "gamma_correction"
    IRIS_BLUR_LARGE     = "iris_blur_large"
@dataclass 
class DegradationStrategy:
    """降质策略数据类"""
    degradation_type: DegradationType    
    enabled: bool = True                 
    config: Optional[DegradationConfig | Dict[str, Any]] = None
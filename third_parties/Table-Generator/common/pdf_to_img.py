from typing import List
from pdflatex import PDFLaTeX
from pdf2image import convert_from_bytes
import numpy as np
# import pyvips # Replaced with pdf2image

# Pdf 2 Imgs
''' Convert pdf to images, so we can convert to images and generate masks.'''
class PdfToImg:
    ''' pdf to img '''
    def pdf_to_img(self,pdf_bytes:bytes):
        # The original implementation using pyvips, commented out.
        # img = pyvips.Image.new_from_buffer(pdf_bytes,options='dpi=200')
        # img_arr = np.ndarray(buffer=img.write_to_memory(),
        #            dtype=np.uint8,
        #            shape=[img.height, img.width, img.bands])
        # img_arr = img_arr[:,:,:3]
        # return img_arr

        # New implementation using pdf2image, which is more windows-friendly.
        # It requires Poppler to be installed and in the system's PATH.
        images = convert_from_bytes(pdf_bytes, dpi=200)
        if not images:
            raise ValueError("PDF to Image conversion failed, no images returned.")
        
        # convert_from_bytes returns a list of PIL images, we take the first page
        img_arr = np.array(images[0])

        # Ensure the image has 3 channels (RGB) by removing the alpha channel if it exists
        if len(img_arr.shape) == 3 and img_arr.shape[2] == 4:
            img_arr = img_arr[:,:,:3]

        return img_arr
    
    ''' pdfs to imgs '''
    def pdfs_to_imgs(self,pdfs:List[bytes]):
        pdf_imgs = []
        for pdf in pdfs:
            img_pdf = self.pdf_to_img(pdf)
            pdf_imgs.append(img_pdf)
        return pdf_imgs

#%%
from typing import List
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from subprocess import run, PIPE
from pdf2image import convert_from_bytes
import docx
from docx.enum.dml import MSO_THEME_COLOR
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
import cv2 as cv
import glob
import re
import itertools
from docx.shared import Inches, Cm
import copy
#%% md
### Word Table Writer
- Top & bottom rows bordered: Medium List
- Header and bottom bordered: Light Shading
- Partially bordered: Colorful Grid
- Fully bordered: Light Grid 

#%%
class TableWriter:    
    def __init__(self):    
        with open('templates/common/config.json','r') as f:
            paragraphs = json.load(f)
        self.paragraphs = []
        for key in paragraphs:
            text = "".join(paragraphs[key])
            self.paragraphs.append(text)
       
    ''' write table '''
    def write_table(self,table,df):
        for j in range(df.shape[-1]):
            table.cell(0,j).text = df.columns[j]
        # add the rest of the data frame
        for i in range(df.shape[0]):
            for j in range(df.shape[-1]):
                table.cell(i+1,j).text = str(df.values[i,j])
                
    'make row of cells background colored, defaults to column header row'
    def color_table(self,table,color='1F5C8B'):
        color = "".join([r'<w:shd {} ','w:fill=',f'"{color}"','/>'])
        for row in table.rows:
            for cell in row.cells:
                shading_elm_2 = parse_xml(color.format(nsdecls('w')))
                cell._tc.get_or_add_tcPr().append(shading_elm_2)
                
    ''' structure margins'''
    def structure_margins(self,doc,vertical=0.5,horizontal=1):
        sections = doc.sections
        for section in sections:
            section.top_margin = Cm(vertical)
            section.bottom_margin = Cm(vertical)
            section.left_margin = Cm(horizontal)
            section.right_margin = Cm(horizontal)

    ''' just write a single table '''
    def add_table(self,doc,table_type:int,df,df_inner=None):
        p = np.random.uniform(0,1)
        if p>0.5:
            accent = ' Accent '+str(int(np.random.uniform(1,7)))
        elif p<0.5:
            accent = ''
        if table_type==0:
            table = doc.add_table(df.shape[0]+1, df.shape[1],style='Medium List 1'+accent)
            self.write_table(table,df)
            return doc
            
        elif table_type==1:
            table = doc.add_table(df.shape[0]+1, df.shape[1],'Light Shading'+accent)
            self.write_table(table,df)
            return doc
        
        elif table_type==2:
            table = doc.add_table(df.shape[0]+1, df.shape[1],'Colorful Grid'+accent)
            self.write_table(table,df)
            return doc
        
        elif table_type==3:
            table = doc.add_table(df.shape[0]+1, df.shape[1],'Light Grid'+accent)
            self.write_table(table,df)
            return doc
            
        elif  table_type==4:
            styles = ['Medium List 1','Light Shading','Colorful Grid','Light Grid']
            style = styles[int(np.random.uniform(0,len(styles)))]
            style = style+accent
            x = int(np.random.uniform(0,df.shape[-1]))
            y = int(np.random.uniform(1,df.shape[-1]))
            table = doc.add_table(df.shape[0]+1, df.shape[1],style='Light Grid'+accent)
            for j in range(df.shape[-1]):
                table.cell(0,j).text = df.columns[j]
            # add the rest of the data frame
            for i in range(df.shape[0]):
                for j in range(df.shape[-1]):
                    if i==x and j==y:
                        inner_table = table.cell(i+1,j).add_table(df_inner.shape[0]+1,df_inner.shape[1])
                        self.write_table(inner_table,df_inner)
                        inner_table.style = style
                    else: 
                        table.cell(i+1,j).text = str(df.values[i,j])
            return doc
             
    ''' write multiple tables '''
    def write(self,table_types:List[int],dfs,inner_dfs=None)->str:
        if inner_dfs is None:
            inner_dfs = [None for i in range(len(table_types))]
        doc = docx.Document()
        self.structure_margins(doc)
        for i in range(len(table_types)):
            text = self.paragraphs[i]
            doc.add_paragraph("".join(["\n",text]))
            header = doc.add_heading('Table '+str(i+1))
            header.alignment = 1
            df = dfs[i]
            if inner_dfs[i] is None:
                doc = self.add_table(doc,table_types[i],dfs[i])
            else:
                doc = self.add_table(doc,table_types[i],dfs[i],inner_dfs[i])
        doc.add_paragraph("".join(["\n\n",self.paragraphs[-1]]))
        doc.save('tmp/x.docx')
        out_doc = docx.Document('tmp/x.docx')
        tables = out_doc.tables
        for table in tables:
            self.color_table(table)
        return doc,out_doc
#%%
df = pd.read_csv('sources/FullData.csv')
df = df.iloc[:5,:4]
#%% md
### Word To Pdf
Convert word document to relevant pdf
#%%
class WordToPdf:
    def doc_to_pdf(self,doc):
        doc.save('tmp/tmp.docx')
        p = run(['libreoffice','--headless','--convert-to','pdf','tmp/tmp.docx','--outdir','tmp'],stdout=PIPE)
        with open('tmp/tmp.pdf','rb') as f:
            pdf = f.read()
        return pdf
    
    def docs_to_pdfs(self,docs):
        pdfs = []
        for doc in docs:
            pdf = self.doc_to_pdf(doc)
            pdfs.append(pdf)
        return pdfs
#%% md
### Pdf To Img
#%%
class PdfToImg:
    ''' pdf to img '''
    def pdf_to_img(self,pdf_bytes:bytes):
        img = convert_from_bytes(pdf_bytes,dpi=200)[0]
        return np.asarray(img,dtype=np.uint8)
    
    ''' pdfs to imgs '''
    def pdfs_to_imgs(self,pdfs:List[bytes]):
        pdf_imgs = []
        for pdf in pdfs:
            img_pdf = self.pdf_to_img(pdf)
            pdf_imgs.append(img_pdf)
        return pdf_imgs
#%% md
### Transformer
Transform a given image , to try and mimic real world data of scanned images. The following transforms applicable
- Gaussian Blur $k$ (kernel size), $(k,k)$ 
- Scale $(sx,sy)$ 
- Rotate $\theta$
#%%
class Transformer:
    ''' blur (! later must investigate scan effect)'''
    def blur(self,img,kernel):
        sigma_x,sigma_y = 2,2
        blurred_img = cv.GaussianBlur(img,kernel,sigma_x,sigma_y)
        return blurred_img
    
    ''' rotate '''
    def rotate(self,img,theta:float,border=(255,255,255)):
        height, width = img.shape[:2]
        center = (width/2, height/2)
        rotate_matrix = cv.getRotationMatrix2D(center=center, angle=theta, scale=1)
        rotated_img = cv.warpAffine(src=img, M=rotate_matrix, dsize=(width, height),borderValue=border)
        return rotated_img
    
    ''' dirtify data by applying sequence of transformations'''
    def dirtify(self,img,k:int,s_x:int,s_y:int,theta:float,mask:bool):
        m,n = img.shape[:2]
        m,n = int(s_y*m),int(s_x*n)
        dim = (n,m)
        if not mask:
            x = self.blur(img,kernel=(k,k))
            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)
            z = self.rotate(y,theta=theta)
        else:
            x = img
            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)
            z = self.rotate(y,theta=theta,border=(0,0,0))
        return z
#%% md
### Mask Generator
From the given pdf image generate table mask (label). This is done as follows.
- Compute absolute difference between raw pdf img and outlined img
- Apply adaptive thresholding to resulting image to obtain binary image
- Detect external contours in binary image and fill bounding box regions of contours
#%%
class PreProcessor:
    ''' grayscale the image '''
    def grayscale(self,img):
        grayscaled = cv.cvtColor(img, cv.COLOR_RGB2GRAY)
        return grayscaled
    
    ''' thresholding the image to a binary image '''
    def threshold(self,img,mode='adaptive'):
        if mode == 'adaptive':
            thresh = cv.adaptiveThreshold(img, 255, 1, 1, 11, 2)
            return thresh
        elif mode=='otsu':
            _,thresh = cv.threshold(img,128,255,cv.THRESH_BINARY |cv.THRESH_OTSU)
            return thresh

    ''' apply preprocessing steps ''' 
    def preprocess(self,img):
        grayscaled = self.grayscale(img)
        thresholded = self.threshold(grayscaled)
        return thresholded
    
class MaskGenerator:
    def __init__(self):
        self.preprocessor = PreProcessor()
       
    ''' fill region with specified contours '''
    def fill(self,shape,contours):
        filled_binary_mask = np.zeros(shape,dtype=np.uint8)
        bounding_rectangles = []
        for contour in contours:
            rect = cv.boundingRect(contour)
            x,y,w,h = rect
            filled_binary_mask[y:y+h,x:x+w] = 255
        return filled_binary_mask
            
    ''' fill the mask '''
    def fill_mask(self,mask):
        binary_mask = self.preprocessor.preprocess(mask)
        contours, _ = cv.findContours(binary_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)
        binary_mask = self.fill(binary_mask.shape,contours)
        return binary_mask
    
    ''' generate table mask taking difference of 2 imgs '''
    def mask(self,raw_img,outlined_img):
        x = raw_img
        y = outlined_img
        mask = abs(x-y)
        filled_mask = self.fill_mask(mask)
        return filled_mask
    
    def masks(self,raw_imgs,outlined_imgs):
        masks = []
        for i in range(len(raw_imgs)):
            x = raw_imgs[i]
            y = outlined_imgs[i]
            mask = self.mask(x,y)
            masks.append(mask)
        return masks
#%% md
### Table Structure Generator
Generate xml like string representing table structure. This is done by making system call to latexml.
#%%
class StructureGenerator:
    def __init__(self):
        self.expression = '<table|<row|<cell|</cell>|</row>|</table>'
        self.regex = re.compile(self.expression)
    
    ''' clean and format str '''
    def clean(self,table:str)->str:
        line = table
        to_remove = ['w:tblPr','w:tblStyle','w:tblW','w:tblLook','w:tblGrid','w:tcPr','w:tblW','w:type','w:tcW']
        for s in to_remove:
            line = line.replace(s,'*')
        to_replace = ['w:tbl','w:tr','w:tc']
        replacement = ['table','row','cell']
        for i in range(len(to_replace)):
            line = line.replace(to_replace[i],replacement[i])
        line = line.replace('cellr','*')
        return line
    
    ''' markup representing table '''
    def structure(self,table:str)->str:
        table = self.clean(table)
        lines = re.findall(self.regex, table)
        lines = [lines[i]+'>' if lines[i].find(">")==-1 else lines[i] for i in range(len(lines))]
        structure = "\n".join(lines)
        return structure
    
    ''' generate table structures '''
    def structures(self,tables:List[str])->List[str]:
        structures = []
        for table in tables:
            structure = self.structure(table._element.xml)
            structures.append(structure)
        return structures
#%% md
### Metadata Generator
Metadata to be generated includes the following
- Number of tables - This is counted by counting the number of connected components in the mask (table regions should be disjoint regions of white pixels)
- Bounding boxes (of tables) - Generated by detecting external contours and returning nounding box
- XML strings of structure - latex string representation as arg yo structure generator
#%%
class MetadataGenerator(StructureGenerator):    
    ''' number of tables from table mask '''
    def number_of_tables(self,table_mask)->int:
        num,_ = cv.connectedComponents(table_mask)
        return num-1
    
    ''' sort the contours top to bottom '''
    def sort(self,contours):
        y_values = []
        for contour in contours:
            x,y,w,h = cv.boundingRect(contour)
            y_values.append(y)
        y_values = np.array(y_values)
        idx = np.argsort(y_values)
        sorted_contours = [contours[i] for i in idx]
        return sorted_contours
    
    ''' bounding boxes '''
    def bounding_boxes(self,table_mask):
        contours , _ = cv.findContours(table_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)
        sorted_contours = self.sort(contours)
        boxes = []
        for contour in sorted_contours:
            box = cv.boundingRect(contour)
            boxes.append(box)
        return boxes
    
    ''' generate metadata '''
    def metadata(self,table_mask,tables:List[str]):
        num_tables = self.number_of_tables(table_mask)
        bboxes = self.bounding_boxes(table_mask)
        bboxes_data = [bbox for bbox in bboxes]
        structures = self.structures(tables)
        result = {'no':num_tables,'bounding_boxes':bboxes_data,'structures':structures}
        return result
#%% md
### Pipeline
Combines all of the above to generate dataset the steps to generate a dataset are.
- Given list of types generate templates (templates func)
- From templates generate resulting pdf,img,mask (datum func)
- Distort datum (applies transforms to img and mask)
- Annotate distort datum (uses metadata generator)
#%%
class DataSource:
    def __init__(self,path):
        self.data = self.load(path)
        self.N = len(self.data)
        self.MIN_ROWS = 2
        self.MIN_OUTER_ROWS = 4
        self.MAX_ROWS = 30
        self.MIN_COLS = 2
        self.MAX_COLS = 7
        self.MAX_COLS_INNER = 3
        self.MAX_COLS_OUTER = 3
        self.STATE = 42
        
    ''' prepare dataframe '''
    def prepare(self,df):
        df.columns = self.format_columns(df.columns)
        nan_value = float("NaN")
        df.replace("", "-", inplace=True)
        df.replace("NaN","-", inplace=True)
        df.dropna(inplace=True)
        df = df.iloc[:300,:]
        df = df.astype(str)
        return df
    
    ''' load dataframes '''
    def load(self,path):
        fnames = glob.glob(path)
        data = []
        for fname in fnames:
            df = pd.read_csv(fname,encoding="utf-8")
            df = self.prepare(df)
            for c in df.columns:
                df[c] = df[c].apply(self.reduce)
            data.append(df)
        return data
    
    ''' reduce rows '''
    def reduce(self,x:str)->str:
        if type(x) is not str:
            return x
        if len(x)>12:
            return x[:12]
        return x
    
    ''' format column headers '''
    def format_columns(self,columns):
        new_columns = []
        for c in columns:
            c = c.replace("_"," ")
            c = c.title()
            if len(c)>12:
                c = c.split(' ')[0][:12]
            new_columns.append(c)
        return new_columns
    
    ''' shuffle '''
    def shuffle(self):
        p = np.random.permutation(self.N)
        self.data = [self.data[p[i]] for i in range(len(p))]
    
    ''' select a dataframe '''
    def select(self,i,rows,cols):
        df = self.data[i].copy()
        df = df.sample(frac=1,random_state=self.STATE)
        df = df.iloc[:rows,np.random.permutation(cols)]
        df = df.iloc[:,:cols]
        return df
        
    ''' sample for simple tables'''
    def sample(self,n,mode=0):
        sample = []
        p = np.random.permutation(n)
        max_rows = self.MAX_ROWS//n
        if mode == 0:
            max_cols = 7
        else:
            max_rows = max(self.MIN_OUTER_ROWS,max_rows//2)
            max_cols = 3
        for i in range(n):
            rows = int(np.random.uniform(self.MIN_ROWS,max_rows+1))
            cols = int(np.random.uniform(self.MIN_COLS,max_cols+1))
            df = self.select(p[i],rows,cols)
            sample.append(df)
        self.shuffle()
        return sample
#%% md
### Pipeline
Combines all of the above to generate dataset the steps to generate a dataset are.
- Given list of types generate templates (templates func)
- From templates generate resulting pdf,img,mask (datum func)
- Distort datum (applies transforms to img and mask)
- Annotate distort datum (uses metadata generator)
#%%
class WordGeneratorPipeline:
    def __init__(self,path):
        self.table_writer = TableWriter()
        self.word_to_pdf = WordToPdf()
        self.pdf_to_img = PdfToImg()
        self.mask_generator = MaskGenerator()
        self.transformer = Transformer()
        self.metadata_generator = MetadataGenerator()
        self.data_source = DataSource(path)
        
    ''' generate simple templates from given dfs and types'''
    def samples(self,types:List[int])->List[str]:
        n = len(types)
        templates = []
        if n<3:
            sample = self.data_source.sample(n,0)
        else:
            sample = self.data_source.sample(n+1,0)
        outer_samples = []
        inner_samples = []
        for i in range(n):
            index = types[i]
            if index!=4:
                outer_samples.append(sample[i])
                inner_samples.append(None)
            else:
                if n == 1:
                    df_outer,df_inner = self.data_source.sample(2,1)[:2]
                elif n==2:
                    df_outer,df_inner = self.data_source.sample(n,1)[:2]
                else:
                    df_outer,df_inner = self.data_source.sample(n+1,1)[:2]
                outer_samples.append(df_outer)
                inner_samples.append(df_inner)
        
        return outer_samples,inner_samples
    
    ''' generate a single datapoint {mask,pdf,tables,img} '''
    def datum(self,types:List[int])->dict:
        outer_samples,inner_samples = self.samples(types)
        # step 1 pdf and outlined pdf
        doc,outlined_doc = self.table_writer.write(types,outer_samples,inner_samples)
        #outlined_doc = self.table_writer.write_outlined(types,outer_samples,inner_samples)
        pdfs = self.word_to_pdf.docs_to_pdfs([doc])
        outlined_pdfs = self.word_to_pdf.docs_to_pdfs([outlined_doc])
          
        
        # step 2 images and masks 
        imgs = self.pdf_to_img.pdfs_to_imgs(pdfs)
        outlined_imgs = self.pdf_to_img.pdfs_to_imgs(outlined_pdfs)
        masks = self.mask_generator.masks(imgs,outlined_imgs)
        
        # step 3 make results
        results = {"mask":masks[0],"img":imgs[0],"pdf":pdfs[0],'tables':doc.tables}
        
        return results
    
    ''' apply transformation using params to dirty data (img and mask) '''
    def distort_datum(self,datum:dict,k:int=7,s_x:int=1,s_y:int=1,theta:float=0)->dict:
        img = datum['img']
        mask = datum['mask']
        img = self.transformer.dirtify(img,k,s_x,s_y,theta,False)
        mask = self.transformer.dirtify(mask,k,s_x,s_y,theta,True)
        return img,mask
    
    ''' label a datum '''
    def label(self,datum:dict):
        mask = datum['mask']
        tables = datum['tables']
        metadata = self.metadata_generator.metadata(mask,tables)
        return metadata
    
    ''' combinations of tables '''
    def generate_combinations(self,types:List[str]):
        combinations = []
        counts = {i:0 for i in types}
        for i in range(1,4):
            c = itertools.combinations(types, i)
            for j in c:
                combinations.append(list(j))
                for k in j:
                    counts[k] = counts[k]+1
        return counts,combinations
    
    ''' save datum along with its annotation '''
    def save(self,datum:dict,annotation:dict,config):
        _id = annotation['id']
        img_path = config['img_path']+_id+'.png'
        mask_path = config['mask_path']+_id+'.png'
        annotation_path = config['annotation_path']+_id+'.json'
        
        # save img and mask
        img = datum['img']
        mask = datum['mask']
        
        cv.imwrite(img_path,img)
        cv.imwrite(mask_path,mask)
        
        # save annotation
        with open(annotation_path,'w') as f:
            json.dump(annotation,f)
        
    ''' generate dataset '''    
    def generate_data(self,config):
        sample_size = config["sample_size"]
        types = config["types"]
        N = sample_size
        counts,combinations = self.generate_combinations(types)
        n = len(combinations)
        stats = {i:0 for i in types}
        _id = 0
        for i in range(N):
            idx = int(np.random.uniform(0,n))
            sub_types = combinations[idx]
            for c in sub_types:
                stats[c] = stats[c]+1
            datum = self.datum(sub_types)
            theta = np.random.uniform(-2,2)
            img,mask = self.distort_datum(datum,theta=theta)
            datum['img'] = img
            datum['mask'] = mask
            label = self.label(datum)
            label["id"] = str(_id)
            self.save(datum,label,config)
            _id =_id+1
        return _id,stats
#%%
path = 'sources/*.csv'
config = {
"sample_size":100,
"types":[0,1,2,3,4],
"types_map":{
    "0":"Top Bottom",
    "1":"Header Bottom",
    "2":"Partially Bordered",
    "3":"Bordered",
    "4":"Embedded",
    },
"img_path":"data/word/imgs/",
"mask_path":"data/word/masks/",
"annotation_path":"data/word/annotations/"
}
#%%
p = WordGeneratorPipeline(path)
#%%
p.generate_data(config)
#%%
# # i am not sure how you are getting your data, but you said it is a
# # pandas data frame
# df = pd.read_csv('sources/FullData.csv')
# df = df.iloc[:5,:4]

# # open an existing document
# doc = docx.Document()

# # add a table to the end and create a reference variable
# # extra row is so we can add the header row
# t = doc.add_table(df.shape[0]+1, df.shape[1],style=t_style)
# # t.style = 'ColorfulShading'
# # t.style._element.xml = p
# # add the header rows.
# for j in range(df.shape[-1]):
#     t.cell(0,j).text = df.columns[j]

# # add the rest of the data frame
# for i in range(df.shape[0]):
#     for j in range(df.shape[-1]):
#         t.cell(i+1,j).text = str(df.values[i,j])

# # save the doc
# f = doc.save(path)
#%%

#%%
# t.cell(0,1).add_table(1,2)
#%%
# collect structure
# jj = t._element.xml
# print(jj)
#%%
# def color_row(row=1):
#     'make row of cells background colored, defaults to column header row'
#     for i in range(1,4):
#         row = t.rows[i]
        
#         for cell in row.cells:
#             shading_elm_2 = parse_xml(r'<w:shd {} w:fill="1F5C8B"/>'.format(nsdecls('w')))
#             cell._tc.get_or_add_tcPr().append(shading_elm_2)
#     f = doc.save('dev.docx')
#%%
# k = t.table._element.
# print(k)
#%%
# t.table._element.xml
#%%

#%%

#%%

#%%

#%%

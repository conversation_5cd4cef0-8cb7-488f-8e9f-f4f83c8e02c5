# TableRender V5.2 并行模式双重背景修复

## 问题描述

在启用`enable_parallel=true`时，TableRender生成的表格图像出现"表格先贴到背景A再贴到背景B"的奇怪现象，导致图像中出现双重背景叠加的视觉效果。

## 根本原因分析

### 1. 双重背景处理机制

TableRender有两个独立的背景处理阶段：

1. **CSS背景渲染阶段**（在`html_renderer.py`中）
   - 通过CSS将背景图直接嵌入到HTML中
   - 浏览器渲染时背景图作为body的background-image

2. **后处理背景合成阶段**（在`image_augmentor.py`中）
   - 将已经渲染好的表格图像再次合成到另一个背景图上

### 2. 并行模式下的问题

**单线程模式**：
- 使用同一个`MainGenerator`实例和`Resolver`实例
- 所有样本的配置解析在同一个上下文中进行
- 背景图选择逻辑一致，不会出现重复背景

**并行模式**：
- 每个线程创建独立的`MainGenerator`和`Resolver`实例
- 每个线程有独立的随机种子和配置解析上下文
- **关键问题**：CSS阶段和后处理阶段可能选择了不同的背景图

### 3. 具体问题流程

```
1. CSS阶段：线程A的Resolver选择背景图A，渲染表格到背景A
2. 后处理阶段：线程A的ImageAugmentor将整个图像（已包含背景A）再贴到背景B上
3. 结果：出现"背景套背景"的双重背景效果
```

## 修复方案

### 1. 添加背景处理模式标识

在`ResolvedPostprocessingParams`中添加`css_background_applied`字段：

```python
# V5.2新增：背景处理模式标识
css_background_applied: bool = Field(default=False, description="CSS阶段是否已应用背景图")
```

### 2. 在Resolver中设置标识

```python
# V5.2新增：判断CSS阶段是否已应用背景图
css_background_applied = (
    apply_background and 
    css_background_width is not None and 
    css_background_height is not None and 
    background_image_path is not None
)
```

### 3. 在ImageAugmentor中避免重复背景处理

```python
# V5.2修复：检查是否已经在CSS阶段应用了背景图
if params.apply_background and params.background_image_path is not None:
    if hasattr(params, 'css_background_applied') and params.css_background_applied:
        # CSS阶段已经应用了背景图，跳过后处理背景合成
        self.logger.info(f"[BACKGROUND_FIX] CSS阶段已应用背景图，跳过后处理背景合成")
        self.logger.info(f"[BACKGROUND_FIX] 避免了双重背景问题（背景A+背景B）")
    else:
        # CSS阶段未应用背景图，正常进行后处理背景合成
        enhanced_image, enhanced_annotations = self._apply_background_composition(...)
```

## 修复效果

### 解决的问题
1. **双重背景叠加**：避免CSS背景和后处理背景的重复应用
2. **视觉异常**：消除"背景套背景"的奇怪视觉效果
3. **并行一致性**：确保并行模式和单线程模式的行为一致

### 保持的功能
1. **CSS背景渲染**：保持CSS背景渲染的高效性和质量
2. **后处理背景合成**：保持后处理背景合成的灵活性
3. **配置兼容性**：保持现有配置文件的完全兼容

## 使用建议

1. **并行模式**：现在可以安全使用并行模式，不会出现双重背景问题
2. **调试模式**：启用debug模式可以观察背景处理的详细日志
3. **配置检查**：如果仍然出现异常，检查日志中的`[BACKGROUND_FIX]`标记

## 版本标识

此修复标记为**TableRender V5.2 并行背景修复**，专门解决并行模式下的双重背景问题。

## 修改的文件

1. `table_render/config.py` - 添加`css_background_applied`字段
2. `table_render/resolver.py` - 设置背景处理模式标识
3. `table_render/postprocessors/image_augmentor.py` - 避免重复背景处理

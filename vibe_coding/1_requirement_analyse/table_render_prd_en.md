# TableRender: Product Requirements Document (PRD)

*This document outlines the product requirements for TableRender, a highly controllable table image synthesis tool. It is intended to be a living document, read and understood by both human developers and AI agents.*

---

## 1. Product Overview (产品概述)

### 1.1. Project Goal (项目目标)

To develop a powerful, end-to-end table image synthesis tool capable of generating a vast and diverse dataset of table images along with precise, structured annotations. The tool aims to serve as a foundational component for training and evaluating table detection, structure recognition, and information extraction models.

### 1.2. Core Problem (解决的核心问题)

Existing table recognition models suffer from a lack of large-scale, high-quality, and diverse training data. Manually creating such datasets is prohibitively expensive and time-consuming. This project addresses this gap by providing a programmatic way to generate synthetic table data that is both realistic and highly customizable, bridging the gap between synthetic simplicity and real-world complexity.

### 1.3. Target Audience (目标用户)

- AI Researchers and Engineers working on Document AI, OCR, and Table Understanding.
- Data Scientists requiring synthetic data for model training and validation.

### 1.4. Guiding Principles (核心设计原则)

- **High Controllability (高度可控性)**: Users must have fine-grained control over every aspect of the table generation, including structure, content, and style, primarily through a single configuration object.
- **Rich Diversity (丰富多样性)**: The tool must be capable of producing a wide variety of table styles and structures, mimicking real-world documents.
- **Data Fidelity (数据保真度)**: The generated images and annotations must be of high quality. The rendering process should be robust, and the annotations must be pixel-accurate and structurally correct.
- **Reproducibility (可复现性)**: Every generated sample must be 100% reproducible from its configuration metadata, ensuring consistency and traceability for experiments.
- **Extensibility (可扩展性)**: The system should be designed with future extensions in mind, allowing for new rendering engines, content sources, or styling options to be added with minimal friction.

---

## 2. System Architecture & Rendering Strategy (系统架构与渲染策略)

### 2.1. Overall Architecture (总体架构)

The system will adopt **Architecture B (Multi-Core Rendering)** as its long-term goal to ensure the highest fidelity for various input formats (e.g., HTML, LaTeX). However, development will be phased.

- **Phase 1: HTML/CSS Rendering Engine.** The initial development will focus exclusively on creating a robust rendering pipeline based on modern web technologies. This engine will handle the generation of tables from programmatic definitions and CSV data, rendering them into images using a headless browser.
- **Phase 2: LaTeX Rendering Engine.** (Future Scope) A separate pipeline for rendering LaTeX-based tables will be developed.
- **Phase 3: Extensible Core.** (Future Scope) The core system will be refactored to support pluggable rendering engines, allowing for easy integration of new formats.

### 2.2. Rendering Pipeline (Phase 1) (第一阶段渲染流水线)

The HTML/CSS rendering pipeline is the core of the initial product version. It follows these logical steps:

1.  **Config Parsing**: The system ingests a user-defined configuration object that dictates all subsequent steps.
2.  **Content Preparation**: Based on the config, content is sourced from either procedural generation (formatted data, random text from a source file) or a specified CSV file.
3.  **Logical Structure Generation**: The table's logical structure (rows, columns, merged cells, headers) is determined based on probabilistic rules in the configuration.
4.  **Internal Data Model Construction**: A clean, format-agnostic internal representation of the table is created. This model separates the table's logic from its presentation.
5.  **HTML/CSS Generation**: The internal data model is translated into HTML and CSS code. Unique IDs are injected into each cell (`<td>`/`<th>`) to facilitate precise annotation.
6.  **Headless Browser Rendering**: The generated HTML/CSS is loaded into a headless browser (e.g., Chrome via Selenium/Playwright) to render the final table image.
7.  **Annotation Generation**: The system queries the rendered page using the injected cell IDs to get precise bounding box (`bbox`) coordinates. This, combined with the internal data model, produces the final JSON annotation, including logical location (`lloc`) and border styles.
8.  **Image Augmentation (Optional)**: The "clean" rendered image can be passed through an augmentation module to apply realistic distortions (e.g., blur, noise, perspective shift).
9.  **Output Packaging**: The final image and its corresponding JSON annotation are saved to the structured output directory.

---

## 3. Functional Requirements (功能需求)

### 3.1. Input: The Configuration Object (输入：配置对象)

The primary input to the system will be a single, comprehensive configuration object (e.g., a Python dictionary or a JSON file). This object serves as the blueprint for a generation task.

- **Format**: The configuration will be a key-value structure.
- **Control**: It will govern all aspects of generation, including content source, table structure, styling, and output settings.
- **API**: The core function will accept this configuration object directly. No fluent/chainable API will be implemented in Phase 1. No preset templates will be provided.

### 3.2. Output: Standardized & Organized (输出：标准化与结构化)

The tool will produce a set of files for each generated batch, organized for clarity and ease of use.

- **File Types**: For each sample, the tool generates:
    - An image file (e.g., `.png`).
    - A JSON annotation file.
    - A metadata file for reproducibility.
- **Directory Structure**:
    - A main output directory is specified by the user.
    - Inside, samples will be grouped into subdirectories, with each subdirectory containing a maximum of 500 samples (e.g., `000001-000500/`, `000501-010000/`, etc.).
    - Within each subdirectory, there will be three parallel folders:
        - `images/`: Contains all `.png` files.
        - `annotations/`: Contains all annotation `.json` files.
        - `metadata/`: Contains all metadata `.json` files, mirroring the structure of the other two folders.
- **Annotation Format**: The annotation JSON will strictly adhere to the `sample_json.json` format, detailing `cells` with their `bbox`, `lloc`, `border`, and `content`.
- **Metadata Format**: The metadata JSON will store the complete configuration object used to generate the corresponding sample, including the random seed, ensuring full reproducibility.

### 3.3. Content Generation (内容生成)

The tool must support multiple, configurable sources for cell content.

- **Procedural Generation**: 
    - **Formatted Data**: Must support the generation of common data types with realistic formats (e.g., dates, currency, percentages).
    - **Text from File**: Must support randomly sampling lines or words from a user-provided `.txt` file to fill cells.
- **CSV Data**: 
    - Must support filling the table structure with data from a specified `.csv` file.
    - **Mismatch Handling**: When the CSV dimensions do not match the target table structure, the behavior will be probabilistically determined by the configuration (e.g., truncate extra columns, leave cells blank, or fill with other content).

### 3.4. Structure Generation (结构生成)

The logical structure of the table is highly configurable and driven by probabilistic rules.

- **Dimensions**: The number of rows and columns can be specified as a fixed number or a random range.
- **Cell Merging**: 
    - The tool must support both row-spanning (`rowspan`) and column-spanning (`colspan`).
    - The occurrence of merged cells will be controlled by a probability setting in the configuration (e.g., `merge_probability: 0.1`).
    - The scope of merges (e.g., max number of rows/columns to span) will also be configurable.
- **Headers**: The system will support the creation of multi-level headers (complex headers).
- **Region-Agnostic**: In Phase 1, the system will not differentiate between `<thead>`, `<tbody>`, and `<tfoot>`. The structure is defined purely by the arrangement of `<th>` and `<td>` tags as generated from the internal model. The use of `<th>` vs `<td>` can be part of the structural configuration.

### 3.5. Style Generation (样式生成)

Styling is a critical component for generating realistic tables and will be applied in a hierarchical manner (Global -> Column -> Row -> Cell).

- **Fonts**:
    - The tool will load font files (`.ttf`, `.otf`) from a user-specified directory.
    - Font family, size, weight (e.g., bold), and style (e.g., italic) will be configurable.
- **Colors**: Both background color and text color of cells will be configurable.
- **Alignment**: Both horizontal (left, center, right) and vertical (top, middle, bottom) alignment of text within cells will be configurable.
- **Borders**: Border styling will be simplified to a boolean state (on/off) for each of the four sides of a cell.
- **Padding**: Cell padding can be specified.
- **Zebra-Striping**: A specific boolean option will be available to apply alternating background colors to rows.
- **Row/Column Sizing**: Row heights and column widths can be randomly stretched or shrunk to create non-uniform layouts, independent of content size.

### 3.6. Boundary Conditions & Error Handling (边界情况与容错性)

The system must be robust and handle edge cases gracefully without crashing.

- **Content Overflow**: If content exceeds cell dimensions, the behavior will be determined by the configuration:
    - **Option A**: Truncate the text.
    - **Option B**: Wrap the text and automatically expand the row height.
    - The choice between A and B can be a random outcome.
- **Configuration Conflicts**: If conflicting style rules are applied to the same element (e.g., a cell is in both a highlighted row and a highlighted column), one rule will be chosen randomly to take precedence. The system will log a warning but will not halt execution.

---

## 4. Non-Functional Requirements (非功能性需求)

### 4.1. Performance (性能)

The system should be optimized for batch generation. While single-image generation time is not the primary metric, the overall throughput for generating thousands of samples should be reasonable. The annotation generation step, in particular, must be efficient and avoid DOM query bottlenecks.

### 4.2. Extensibility (可扩展性)

The architecture should be modular. Key components like the content generator, structure generator, and style mapper should be designed as distinct modules to facilitate future enhancements. Interfaces for these modules should be considered during initial implementation, even if a full plugin system is not built in Phase 1.

### 4.3. Usability (易用性)

The tool will be operated via a clear configuration object. While the configuration may be complex, its structure should be logical and well-documented.

### 4.4. Reliability (可靠性)

The tool must be able to run large generation jobs unattended without crashing. The error handling for configuration conflicts and boundary conditions is critical to this requirement.

---

## 5. Scope (范围)

### 5.1. In Scope for Phase 1 (第一阶段范围内)

- End-to-end HTML/CSS rendering pipeline.
- All functional requirements listed in Section 3, including probabilistic generation of structure, content, and style.
- Generation of "clean" digital-native images and optional post-processing for augmentation.
- Highly structured, reproducible output with separated images, annotations, and metadata.

### 5.2. Out of Scope for Phase 1 (第一阶段范围外)

- **LaTeX Rendering Engine**: This is a planned future feature.
- **Advanced GUI/API**: No graphical user interface or fluent/chainable API will be developed.
- **Advanced Presets/Templates**: The system will not have a built-in template system.
- **Database Integration**: Content sourcing from databases is not in scope.
- **Real-time Generation**: The tool is designed for offline batch processing, not real-time applications.

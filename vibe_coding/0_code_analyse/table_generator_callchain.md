# Table-Generator 调用链分析

## 调用链

### 节点: main

*   **所在代码文件**: `third_parties/Table-Generator/main.py`
*   **用途**: 项目的主入口函数，负责根据配置文件 `config.json` 初始化并运行表格生成流水线。它可以选择串行或并行模式生成LaTeX、HTML和Word格式的表格。
*   **输入参数**:
    *   `config` (dict): 从 `config.json` 加载的配置字典，包含了并行/串行模式开关、各类表格（LaTeX, HTML, Word）的生成参数以及数据源路径等信息。
*   **输出说明**:
    *   无直接返回值。
    *   该函数会在磁盘上生成表格图像文件。
    *   会在控制台打印生成过程的状态信息和耗时。
    *   会生成性能分析文件 (`.profile`) 到 `profiles/` 目录下。
*   **核心调用**:
    *   `json.load()`: 读取 `config.json` 配置文件。
    *   `LatexGeneratorPipeline(path)`: 初始化LaTeX表格生成管道。
    *   `WordGeneratorPipeline(path)`: 初始化Word表格生成管道。
    *   `HtmlGeneratorPipeline(path)`: 初始化HTML表格生成管道。
    *   `serial_run()` 或 `parallel_run()`: 根据配置选择执行方式。
        *   `serial_run` -> `generate_serial` -> `pipeline.generate_data()`
        *   `parallel_run` -> `generate_parallel` -> `pipeline.generate_data()`

### 节点: LatexGeneratorPipeline

*   **所在代码文件**: `third_parties/Table-Generator/Latex/pipeline.py`
*   **用途**: 负责生成LaTeX格式的表格数据。它封装了从数据源获取数据、生成LaTeX模板、编译成PDF、转换成图像、生成掩码、进行图像变换和生成标注的完整流程。
*   **核心方法**:
    *   `__init__(self, path)`: 构造函数。初始化数据源 `DataSource` 以及一系列处理模块，包括 `TemplateGenerator`, `TableGenerator`, `TableWriter`, `PdfGenerator`, `PdfToImg`, `MaskGenerator`, `Transformer`, 和 `Labeler`。
    *   `generate_data(self, config)`: 串行生成数据集。根据配置循环生成指定数量的样本。
    *   `generate_data_parallel(self, config)`: 并行生成数据集。使用 `ThreadPoolExecutor` 并行处理数据生成过程。
*   **核心调用流程 (`generate_data`/`generate_data_parallel`)**:
    1.  `generate_combinations()`: 创建不同表格类型的组合。
    2.  `datum(sub_types)`: 为一个给定的类型组合生成一个数据点。
        *   `templates()`: 根据类型生成LaTeX模板字符串。
        *   `table_generator.tables()`: 基于模板生成表格结构。
        *   `table_writer.write()`: 将表格结构写入完整的LaTeX文档字符串。
        *   `pdf_generator.pdf()`: 将LaTeX字符串编译成PDF文件。
        *   `pdf_to_img.pdfs_to_imgs()`: 将PDF文件栅格化为图像。
        *   `mask_generator.masks()`: 从普通图像和带轮廓的图像中生成掩码。
    3.  `distort_datum(datum, ...)`: 对生成的图像和掩码应用随机的仿射变换和噪声。
        *   `transformer.dirtify()`: 执行实际的图像变换操作。
    4.  `label(datum)`: 为生成的数据创建标注信息。
        *   `labeler.label()`: 分析掩码和表格结构，生成包含单元格坐标、行列信息的JSON标注。
    5.  `save(datum, label, config)`: 将生成的图像、掩码和标注JSON文件保存到磁盘。

### 节点: HtmlGeneratorPipeline

*   **所在代码文件**: `third_parties/Table-Generator/Html/pipeline.py`
*   **用途**: 负责生成HTML格式的表格数据。其结构和流程与 `LatexGeneratorPipeline` 非常相似，但使用专门处理HTML的模块。
*   **核心方法**:
    *   `__init__(self, path)`: 构造函数。初始化 `DataSource` 和一系列处理模块，但使用 `Html` 目录下的 `TemplateGenerator`, `TableGenerator`, `TableWriter`, `PdfGenerator`, 和 `StructureGenerator`。
    *   `generate_data(self, config)`: 串行生成数据集。
    *   `generate_data_parallel(self, config)`: 并行生成数据集。
*   **核心调用流程 (`generate_data`/`generate_data_parallel`)**:
    1.  `generate_combinations()`: 创建不同表格类型的组合。
    2.  `datum(sub_types)`: 为一个给定的类型组合生成一个数据点。
        *   `templates()`: 根据类型生成HTML模板字符串。
        *   `table_generator.tables()`: 基于模板生成表格结构。
        *   `table_writer.write()`: 将表格结构写入完整的HTML文档字符串。
        *   `pdf_generator.pdf()`: 使用wkhtmltopdf将HTML字符串转换成PDF文件。
        *   `pdf_to_img.pdfs_to_imgs()`: 将PDF文件栅格化为图像。
        *   `mask_generator.masks()`: 生成掩码。
    3.  `distort_datum(datum, ...)`: 对图像和掩码应用变换。
    4.  `label(datum)`: 为数据创建标注。
    5.  `save(datum, label, config)`: 保存结果。

### 节点: WordGeneratorPipeline

*   **所在代码文件**: `third_parties/Table-Generator/Word/pipeline.py`
*   **用途**: 负责生成基于Microsoft Word的表格数据。它通过编程方式创建Word文档，将其转换为PDF，然后进行与其它pipeline类似的图像处理和标注流程。
*   **核心方法**:
    *   `__init__(self, path)`: 构造函数。初始化 `DataSource` 和一系列处理模块，包括 `TableWriter`, `WordToPdf`, `PdfToImg`, `MaskGenerator`, `Transformer`, 和 `Labeler`。
    *   `generate_data(self, config)`: 串行生成数据集。
*   **核心调用流程 (`generate_data`)**:
    1.  `generate_combinations()`: 创建不同表格类型的组合。
    2.  `datum(sub_types)`: 为一个给定的类型组合生成一个数据点。
        *   `samples()`: 从数据源获取用于填充表格的 `DataFrame`。
        *   `table_writer.write()`: 使用 `python-docx` 库创建包含表格的Word文档对象。
        *   `word_to_pdf.docs_to_pdfs()`: 通过COM互操作或`unoconv`将Word文档转换为PDF。
        *   `pdf_to_img.pdfs_to_imgs()`: 将PDF文件栅格化为图像。
        *   `mask_generator.masks()`: 生成掩码。
    3.  `distort_datum(datum, ...)`: 对图像和掩码应用变换。
    4.  `label(datum)`: 为数据创建标注。
    5.  `save(datum, label, config)`: 保存结果。

## 整体用途

`Table-Generator` 项目是一个复杂的数据合成引擎，其核心目标是为机器学习，特别是计算机视觉领域的表格理解任务，生成大规模、多样化且高质量的训练和测试数据集。它通过三种不同的源（LaTeX、HTML、Word）来创建表格，每种源都有自己的一套样式和模板，从而确保生成数据的多样性。

该项目的整体工作流程可以概括为：

1.  **数据驱动生成**：从CSV文件中读取原始数据作为表格内容的基础。
2.  **多源渲染**：通过三个独立的流水线（`LatexGeneratorPipeline`, `HtmlGeneratorPipeline`, `WordGeneratorPipeline`），以编程方式将数据渲染成不同格式的文档（.tex, .html, .docx）。
3.  **标准化为PDF**：将这些不同格式的文档统一转换为PDF格式，作为后续图像处理的中间标准。
4.  **图像与掩码提取**：将PDF文件栅格化为PNG图像，并利用一个巧妙的“轮廓”版本PDF来精确地生成像素级的表格结构掩码（mask）。
5.  **数据增强**：对生成的“干净”图像和掩码应用随机的仿射变换和噪声，以模拟真实世界中可能出现的文档扫描、拍照失真等情况。
6.  **结构化标注**：最关键的一步，项目不仅生成图像，还为每个表格生成了详细的JSON格式标注，其中包含了每个单元格的精确位置（bounding box）、行列索引、以及可能的合并单元格信息。
7.  **灵活配置与并行处理**：整个过程由一个 `config.json` 文件驱动，用户可以方便地配置生成数量、样式类型等参数。同时，项目支持并行处理，可以显著加快大规模数据集的生成速度。

最终，该项目产出的是一一对应的“图像-掩码-标注”三元组，为训练深度学习模型（如用于表格结构识别的CNN或Transformer模型）提供了理想的输入。

## 目录结构

以下是调用链涉及的核心文件及目录结构：

```
third_parties/Table-Generator/
├── main.py                 # 项目入口，解析配置，调度Pipelines
├── config.json             # 配置文件，定义生成参数
├── common/                 # 存放通用的辅助模块
│   ├── data_source.py      # 从CSV文件读取数据
│   ├── labeler.py          # 生成结构化标注
│   ├── mask_generator.py   # 生成掩码
│   ├── pdf_to_img.py       # PDF转图像
│   └── transformer.py      # 图像变换与增强
├── Latex/                  # LaTeX流水线相关模块
│   ├── pipeline.py         # LaTeX流水线主类
│   ├── templator.py        # LaTeX简单模板
│   ├── complex_templator.py# LaTeX复杂模板
│   ├── table_generator.py  # 生成LaTeX表格代码
│   ├── table_writer.py     # 写入完整的.tex文件
│   ├── pdf_generator.py    # .tex编译成.pdf
│   └── structure_generator.py # 为Labeler提供结构信息
├── Html/                   # HTML流水线相关模块
│   ├── pipeline.py         # HTML流水线主类
│   ├── templator.py        # HTML模板
│   ├── ...                 # (结构与Latex类似)
│   └── pdf_generator.py    # HTML转PDF (使用wkhtmltopdf)
├── Word/                   # Word流水线相关模块
│   ├── pipeline.py         # Word流水线主类
│   ├── table_writer.py     # 使用python-docx创建Word表格
│   ├── word_to_pdf.py      # Word转PDF (使用COM或unoconv)
│   └── structure_generator.py # 为Labeler提供结构信息
└── sources/                # 存放作为数据源的CSV文件
```

## 调用时序图

以下时序图展示了以`LatexGeneratorPipeline`为例，生成一个数据点的典型调用流程。

```mermaid
sequenceDiagram
    participant M as main.py
    participant LP as Latex/pipeline.py
    participant DS as common/data_source.py
    participant TG as Latex/table_generator.py
    participant TW as Latex/table_writer.py
    participant PG as Latex/pdf_generator.py
    participant P2I as common/pdf_to_img.py
    participant MG as common/mask_generator.py
    participant TR as common/transformer.py
    participant L as common/labeler.py

    M->>LP: generate_data(config)
    LP->>LP: generate_combinations(types)
    loop for each combination
        LP->>LP: datum(sub_types)
        activate LP
        LP->>DS: sample(n)
        DS-->>LP: dataframes
        LP->>TG: tables(templates)
        TG-->>LP: table_structures
        LP->>TW: write(table_structures)
        TW-->>LP: tex_string
        LP->>PG: pdf(tex_string)
        PG-->>LP: pdf_file
        LP->>P2I: pdfs_to_imgs([pdf_file])
        P2I-->>LP: image
        LP->>MG: masks([img], [outlined_img])
        MG-->>LP: mask
        deactivate LP
        LP->>LP: distort_datum(datum)
        activate LP
        LP->>TR: dirtify(image, mask)
        TR-->>LP: distorted_img, distorted_mask
        deactivate LP
        LP->>LP: label(datum)
        activate LP
        LP->>L: label(mask, tables)
        L-->>LP: annotation_json
        deactivate LP
        LP->>LP: save(datum, annotation, config)
    end
    LP-->>M: Done
```


# TableImageGenerator 调用链分析

## 调用链

### 节点: TableImageGenerator.create_table_image
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 作为表格图片生成的主要入口函数，接收表格数据和各种配置选项，协调整个图片生成流程，包括数据准备、表格渲染、可选的横幅创建和最终的图像合成，并将最终图像保存到指定位置。
*   **输入参数**:
    *   `data` (list): 表格数据，一个由字典组成的列表，每个字典代表一行。
    *   `output_file` (str): 输出图片的文件路径。
    *   `columns_order` (list, optional): 用于指定列的显示顺序。
    *   `banner_path` (str, optional): Banner图片的路径。
    *   `banner_text` (str, optional): Banner上要显示的文字。
    *   `color_column` (str, optional): 需要根据内容进行颜色渲染的列名。
    *   `multi_columns` (list, optional): 多级表头的配置。
    *   `column_display` (dict, optional): 列名到显示名称的映射。
    *   `replace_zero` (bool, optional): 是否将数值0替换为'-'。
    *   `highlight_rules` (dict, optional): 定义了行高亮规则，格式为 `{'列名': '关键字'}`。
*   **输出说明**:
    *   `str`: 成功时返回生成的图片文件的完整路径。
*   **调用关系**:
    *   `_build_table_data(...)`
    *   `_create_table(...)`
    *   `_create_banner_image(...)` (如果使用banner)
    *   `_merge_images(...)` (如果使用banner)

### 节点: TableImageGenerator._build_table_data
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 负责将原始输入数据转换为适合表格渲染的结构化格式。它处理过滤掉无数据的空列，根据配置构建单级或多级表头，并将列的内部键名映射到其最终的显示名称。同时，它还根据过滤和排序后的列准备数据行。
*   **输入参数**:
    *   `data` (list): 原始表格数据，一个由字典组成的列表。
    *   `columns_order` (list): 指定列的显示顺序。
    *   `multi_columns` (list): 多级表头的配置。
    *   `column_display` (dict): 列键到显示名称或格式化类型的映射。
    *   `replace_zero` (bool): 是否将0替换为'-'的标志，此参数会传递给 `_process_value` 函数。
*   **输出说明**:
    *   `dict`: 一个包含两个键的字典：`headers` (代表表格头部的列表的列表) 和 `data` (代表表格行的列表的列表)。
*   **调用关系**:
    *   `_process_value(...)`

### 节点: TableImageGenerator._process_value
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 对单个单元格的值进行格式化处理。这包括将空值或None统一替换为'-'，根据`replace_zero`参数决定是否将数值0也替换为'-'，以及根据`format_type`参数将时间戳转换为指定格式的日期或时间字符串。
*   **输入参数**:
    *   `value` (str): 单元格的原始值。
    *   `replace_zero` (bool, optional): 如果为True，则将数值0替换为'-'。
    *   `format_type` (str, optional): 格式化类型，支持'to_format'（完整时间）和'to_day'（月日）。
*   **输出说明**:
    *   `str`: 经过处理和格式化后的字符串值。

### 节点: TableImageGenerator._create_table
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 核心的表格绘制函数。它接收JSON格式的结构化表格数据，负责计算表格的整体尺寸，并根据DPI（分辨率）进行缩放以生成高分辨率图像。它迭代处理表头和数据行，处理单元格的合并，并调用 `_draw_cell` 来绘制每个单元格，包括其背景、边框和文本。
*   **输入参数**:
    *   `json_data` (str): JSON格式的字符串，包含了表格的 `headers` 和 `data`。
    *   `color_column` (str, optional): 需要应用特殊颜色映射的列名。
    *   `replace_zero` (bool, optional): 是否将数值0替换为'-'。
    *   `highlight_rules` (dict, optional): 用于高亮特定行的规则。
    *   `dpi` (int, optional): 生成图片的DPI，默认为300。
*   **输出说明**:
    *   `Image`: 一个Pillow `Image` 对象，包含了绘制好的表格图像。
*   **调用关系**:
    *   `_calculate_table_size(...)`
    *   `_draw_cell(...)`

### 节点: TableImageGenerator._calculate_table_size
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 根据表头和数据行的内容，动态计算并确定整个表格的最终宽度和高度。它首先确定表格的总列数，然后基于目标总宽度（如1920像素减去边距）和预设的最小/最大单元格宽度，计算出最合适的单个单元格宽度。最终，它返回表格渲染所需的总宽度和总高度。
*   **输入参数**:
    *   `headers` (List[List[Optional[Cell]]]): 表头部分的结构，一个由Cell对象组成的列表的列表。
    *   `data` (List[List[str]]): 表格数据行，一个由字符串组成的列表的列表。
*   **输出说明**:
    *   `Tuple[int, int]`: 一个包含两个整数的元组，分别代表计算出的表格总宽度和总高度。

### 节点: TableImageGenerator._draw_cell
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 负责在图像上绘制单个单元格。它处理单元格的合并（跨行/跨列），设置背景色（区分表头、普通行、高亮行），绘制边框，并根据单元格内容和状态（如特殊颜色映射）设置文本颜色。它还会调用 `_process_value` 来格式化文本，并计算文本位置以实现居中显示。
*   **输入参数**:
    *   `draw` (ImageDraw): Pillow `ImageDraw` 对象，用于在图像上进行绘制。
    *   `x`, `y` (int): 单元格在图像上的左上角坐标。
    *   `cell` (Union[Cell, str]): 要绘制的单元格对象或文本内容。
    *   `is_header` (bool, optional): 标记是否为表头单元格。
    *   `row_idx` (int, optional): 当前行的索引，用于交替行颜色。
    *   `color_column` (str, optional): 需要应用颜色映射的列名。
    *   `column_name` (str, optional): 当前单元格所属的列名。
    *   `replace_zero` (bool, optional): 是否将0替换为'-'。
    *   `highlight` (bool, optional): 是否高亮当前单元格所在的行。
*   **输出说明**: 无返回值，直接在传入的 `ImageDraw` 对象上进行绘制操作。
*   **调用关系**:
    *   `_process_value(...)`
    *   `_hex_to_rgb(...)`

### 节点: TableImageGenerator._hex_to_rgb
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 一个工具函数，用于将CSS风格的十六进制颜色代码（例如 '#FFFFFF'）转换为Pillow库可以使用的RGB元组（例如 (255, 255, 255)）。
*   **输入参数**:
    *   `hex_color` (str): 十六进制颜色字符串。
*   **输出说明**:
    *   `tuple`: 对应于输入颜色的RGB元组。

### 节点: TableImageGenerator._create_banner_image
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 创建一个包含背景图片和可选标题文本的横幅（Banner）图像。它会加载指定的背景图片，将其调整到固定的宽度，并在其下方添加居中的标题文本。最终生成的横幅图像被保存到指定的输出路径。
*   **输入参数**:
    *   `banner_path` (str): 背景图片的路径。
    *   `banner_text` (str): 要在横幅上显示的标题文本。
    *   `output_path` (str): 生成的横幅图像的保存路径。
*   **输出说明**: 无返回值，直接将生成的图像保存到文件。

### 节点: TableImageGenerator._merge_images
*   **所在代码文件相对路径**: `third_parties/TableImageGenerator/table.py`
*   **用途**: 将生成的横幅图像和表格图像垂直合并成一张最终的图片。它会加载两个图像，将表格图像的宽度调整为与横幅图像内容区域的宽度一致，然后将它们粘贴到一个新的空白图像上，并最终以高质量的PNG格式保存到输出路径。
*   **输入参数**:
    *   `banner_image` (str): 横幅图像的文件路径。
    *   `table_image` (str): 表格图像的文件路径。
    *   `output_path` (str): 最终合并后的图像的保存路径。
*   **输出说明**: 无返回值，直接将合并后的图像保存到文件。

## 整体用途

该调用链的整体用途是提供一个完整的、从数据到图像的金融表格生成解决方案。它以结构化的数据（如字典列表）为输入，通过一系列灵活的配置选项（如列排序、多级表头、条件高亮、自定义颜色等），最终生成一张清晰、专业、可定制的表格图片。该模块不仅处理了核心的表格绘制逻辑，还支持添加带有标题的横幅，使得生成的图片信息更完整，适用于报告、展示等多种场景。

## 目录结构

```
third_parties/TableImageGenerator
└── table.py
```

## 调用时序图

```mermaid
sequenceDiagram
    participant Client
    participant TIG as third_parties/TableImageGenerator/table.py

    Client->>+TIG: create_table_image(data, output_file, banner_path, ...)
    TIG->>+TIG: _build_table_data(data, ...)
    TIG-->>-TIG: return structured_data
    TIG->>+TIG: _create_table(structured_data, ...)
    TIG->>+TIG: _calculate_table_size(...)
    TIG-->>-TIG: return width, height
    TIG->>TIG: loop for each cell
    TIG->>+TIG: _draw_cell(...)
    TIG-->>-TIG: 
    TIG->>TIG: end
    TIG-->>-TIG: return table_image
    TIG->>+TIG: _create_banner_image(...)
    TIG-->>-TIG: 
    TIG->>+TIG: _merge_images(...)
    TIG-->>-TIG: 
    TIG-->>-Client: return file_path
```

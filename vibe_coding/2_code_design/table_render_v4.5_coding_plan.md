# TableRender V4.5 编码计划文档

## 版本概述

TableRender V4.5版本在V4.4降质功能基础上，新增两个核心颜色功能：
1. **线条颜色多样化**：支持边框线条使用多种颜色，不再局限于黑色
2. **颜色差异驱动的边框标注**：基于相邻单元格颜色差异自动调整边框标注逻辑

## 核心特性

- **线条颜色概率化控制**：通过 `randomize_border_color_probability` 控制线条颜色随机化
- **线条颜色对比度保证**：确保线条颜色与相邻单元格背景色有足够对比度
- **颜色差异检测**：基于RGB差值检测相邻单元格颜色差异
- **智能边框标注**：颜色差异驱动的边框标注逻辑，提升训练数据质量
- **合并单元格支持**：完整支持各种合并单元格场景的边框处理

## 受影响的现有模块

### 核心模块修改
- **`table_render/config.py`**: 新增线条颜色配置项
- **`table_render/utils/color_utils.py`**: 扩展边框颜色生成和对比度计算
- **`table_render/models.py`**: 扩展边框样式模型，支持颜色信息
- **`table_render/builders/style_builder.py`**: 修改CSS生成逻辑，支持动态边框颜色
- **`table_render/utils/annotation_converter.py`**: 新增颜色差异检测和边框标注调整

### 配置文件适配
- **配置文件**: 需要更新默认配置，添加线条颜色相关参数

## 渐进式小步迭代开发步骤

### 步骤1: 配置系统扩展
**目标**: 为线条颜色功能添加配置支持

**操作**:
1. 修改 `table_render/config.py`:
   - 在 `CommonStyleConfig` 中新增 `randomize_border_color_probability: float` 字段
   - 确保复用现有的 `ColorContrastConfig` 用于线条颜色对比度控制
2. 更新配置验证逻辑，确保新字段的有效性检查
3. 创建测试配置文件验证配置加载

**验证标准**:
- 配置文件能正常加载，包含新的线条颜色配置项
- 程序启动正常，配置验证通过
- 可以通过日志确认新配置项被正确读取

### 步骤2: 边框样式模型扩展
**目标**: 扩展数据模型以支持边框颜色信息

**操作**:
1. 修改 `table_render/models.py`:
   - 扩展 `CellBorderStyle` 类，新增四个颜色字段：
     - `top_color: str = "#000000"`
     - `right_color: str = "#000000"`
     - `bottom_color: str = "#000000"`
     - `left_color: str = "#000000"`
2. 确保向后兼容性，默认值保持黑色
3. 更新相关的字符串表示方法

**验证标准**:
- 数据模型扩展完成，不影响现有功能
- 程序能正常创建和使用扩展后的边框样式对象
- 默认情况下边框颜色仍为黑色，保持现有视觉效果

### 步骤3: 颜色管理器功能扩展
**目标**: 扩展ColorManager以支持边框颜色生成和对比度计算

**操作**:
1. 修改 `table_render/utils/color_utils.py`:
   - 新增 `get_border_color()` 方法，支持边框颜色生成
   - 新增 `calculate_average_contrast()` 方法，计算与两个背景色的平均对比度
   - 新增 `ensure_border_color_contrast()` 方法，确保边框颜色满足对比度要求
2. 复用现有的颜色生成逻辑和对比度计算函数
3. 添加单元测试验证新方法的正确性

**验证标准**:
- 新的颜色生成方法能正确工作
- 对比度计算逻辑正确，能处理各种颜色组合
- 单元测试通过，验证边界情况处理

### 步骤4: 样式构建器边框颜色集成
**目标**: 在样式构建阶段集成边框颜色生成逻辑

**操作**:
1. 修改 `table_render/builders/style_builder.py`:
   - 在 `_generate_border_rules_from_cells()` 方法中集成边框颜色生成
   - 为每个单元格的每条边框生成颜色（如果启用随机化）
   - 修改CSS生成逻辑，支持为每个单元格生成具体的边框颜色样式
2. 确保在边框决策完成后进行颜色生成
3. 保持与现有边框模式（full/none/semi）的兼容性

**验证标准**:
- 能够生成包含边框颜色的CSS样式
- 不同的 `randomize_border_color_probability` 值产生不同的视觉效果
- 现有的边框模式功能不受影响

### 步骤5: 颜色差异检测核心功能
**目标**: 实现相邻单元格颜色差异检测算法

**操作**:
1. 在 `table_render/utils/color_utils.py` 中新增:
   - `calculate_rgb_difference()` 方法：计算两个颜色的RGB差值和
   - `are_colors_similar()` 方法：判断两个颜色是否相似（差值和<10）
   - `is_default_color_pair()` 方法：判断是否为默认颜色组合
2. 添加完整的单元测试，覆盖各种颜色组合
3. 处理边界情况，如无效颜色值的容错

**验证标准**:
- 颜色差异检测算法正确工作
- 能正确识别默认颜色和非默认颜色
- 单元测试覆盖率高，边界情况处理正确

### 步骤6: 边框标注调整逻辑实现
**目标**: 实现基于颜色差异的边框标注调整功能

**操作**:
1. 修改 `table_render/utils/annotation_converter.py`:
   - 新增 `_detect_color_based_borders()` 方法
   - 新增 `_get_adjacent_cells()` 方法，处理相邻关系（包括合并单元格）
   - 新增 `_should_force_border()` 方法，判断是否因颜色差异强制显示边框
2. 在 `convert_to_final_format()` 中集成颜色差异检测
3. 确保标注调整逻辑在边框决策之后执行

**验证标准**:
- 相邻单元格颜色不同时，边框标注正确调整为有边
- 合并单元格的边框处理逻辑正确
- 标注结果与视觉感知一致

### 步骤7: 合并单元格边框处理优化
**目标**: 完善合并单元格场景下的边框处理逻辑

**操作**:
1. 扩展 `table_render/utils/annotation_converter.py`:
   - 实现 `_handle_merged_cell_borders()` 方法
   - 处理"有一半有边就算有边"的规则
   - 确保相邻单元格边框标注的一致性
2. 添加针对各种合并场景的测试用例
3. 处理表头与主体之间的边界情况

**验证标准**:
- 各种合并单元格场景的边框处理正确
- 边框标注在相邻单元格间保持一致
- 表头与主体边界处理符合预期

### 步骤8: 集成测试和配置优化
**目标**: 完成功能集成，优化配置和性能

**操作**:
1. 创建完整的集成测试用例，覆盖各种场景组合
2. 优化配置文件，提供合理的默认值
3. 添加详细的日志输出，便于调试和监控
4. 性能测试和优化，确保新功能不显著影响生成速度

**验证标准**:
- 所有功能正常工作，新旧功能兼容
- 配置文件完整，文档清晰
- 性能表现可接受，无明显性能退化

## 实现流程图

```mermaid
flowchart TD
    A[配置系统扩展] --> B[边框样式模型扩展]
    B --> C[颜色管理器功能扩展]
    C --> D[样式构建器边框颜色集成]
    D --> E[颜色差异检测核心功能]
    E --> F[边框标注调整逻辑实现]
    F --> G[合并单元格边框处理优化]
    G --> H[集成测试和配置优化]
    
    subgraph "核心功能模块"
        C
        E
        F
    end
    
    subgraph "集成模块"
        D
        G
    end
    
    subgraph "基础设施"
        A
        B
        H
    end
```

## 关键技术要点

### 1. 边框颜色生成策略
- 复用现有的颜色生成框架
- 独立的概率控制机制
- 与相邻单元格背景色的对比度保证

### 2. 颜色差异检测算法
- RGB差值和计算：`|R1-R2| + |G1-G2| + |B1-B2| < 10`
- 默认色特殊处理逻辑
- 高效的颜色比较实现

### 3. 合并单元格处理规则
- "有一半有边就算有边"的逻辑实现
- 相邻关系的准确识别
- 边框标注一致性保证

### 4. 向后兼容性保证
- 默认配置保持现有行为
- 渐进式功能启用
- 现有API接口不变

## 风险控制

### 1. 功能风险
- 每步完成后进行回归测试
- 保持现有功能的稳定性
- 新功能可选择性启用

### 2. 性能风险
- 颜色差异检测的计算复杂度控制
- 大表格场景的性能测试
- 必要时进行算法优化

### 3. 兼容性风险
- 配置文件向后兼容
- API接口保持稳定
- 渐进式迁移策略

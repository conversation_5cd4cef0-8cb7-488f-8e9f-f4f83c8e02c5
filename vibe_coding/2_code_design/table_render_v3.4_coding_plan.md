# TableRender v3.4 开发计划：概率化配置

**版本:** 3.4
**目标:** 引入概率化配置机制，提升单一配置文件的表格生成多样性，增强泛化能力。

---

## 1. 整体设计与代码结构

本次升级的核心是将用户配置中的静态值（如 `rows: 10`）改造为可支持概率分布的结构。我们将引入一个新的概率处理工具模块，更新数据模型，并重点重构配置解析器（`Resolver`）以实现新逻辑。

### 1.1. 受影响的模块

- **`table_render/config.py`**: 配置模型，需要被重点修改以支持新的概率化结构和可变sizing配置。
- **`table_render/resolver.py`**: 配置解析器，是本次修改的核心，负责将概率化配置解析为确定性参数。
- **`table_render/utils/`**: 将创建一个新的 `prob_utils.py` 模块，用于封装概率计算和随机选择的通用逻辑。
- **`table_render/builders/style_builder.py`**: 样式构建器，需要更新以支持可变行高列宽的CSS生成。
- **`table_render/renderers/html_renderer.py`**: HTML渲染器，需要更新以应用新的CSS类名。
- **`table_render/utils/style_utils.py`**: 样式工具，需要更新以支持概率化配置。
- **`configs/`**: 将创建一个新的 `v3.4_default.yaml` 配置文件来演示和测试新功能。

### 1.2. 最终代码目录结构 (实际)

```
table_render/
|-- builders/               # 构建器模块
|   |-- style_builder.py      # 重点修改，支持可变sizing的CSS生成
|   |-- structure_builder.py  # 基本不受影响
|   `-- content_builder.py    # 基本不受影响
|-- renderers/              # 渲染器模块
|   `-- html_renderer.py      # 轻微修改，更新CSS类名应用
|-- utils/
|   |-- prob_utils.py         # [新增] 概率处理工具模块
|   |-- style_utils.py        # 修改，支持概率化配置
|   |-- font_utils.py         # 基本不受影响
|   |-- color_utils.py        # 基本不受影响
|   `-- file_utils.py         # 轻微修改，支持label_suffix
|-- config.py               # 重点修改，定义新的概率化配置模型
|-- models.py               # 基本不受影响
|-- main.py                 # 入口 (基本不受影响)
|-- main_generator.py       # 主生成器 (轻微修改，传递label_suffix)
`-- resolver.py             # 配置解析器 (将被重构)
```

---

## 2. 渐进式开发步骤

### 步骤 1: (新增) 实现标注文件后缀功能

**目标:** 在输出配置中增加一个可选的 `label_suffix` 字段，用于为生成的标注文件添加后缀，以区分图像和标注文件。

**操作:**
1.  在 `table_render/models/config_models.py` 的 `OutputConfig` 模型中，添加一个新字段：
    ```python
    # table_render/models/config_models.py
    class OutputConfig(BaseModel):
        # ... aisting fields
        label_suffix: Optional[str] = None # 新增字段
    ```
2.  修改 `table_render/resolver.py` 中的 `_resolve_output_params` 方法，将此后缀传递给解析后的参数对象。
3.  修改 `table_render/main_generator.py` 中调用文件保存工具（如 `FileUtils.save_sample`）的地方，使其能够根据 `label_suffix` 构建正确的标注文件名。
    -   如果 `label_suffix` 存在，文件名应为 `{basename}{suffix}.json`。
    -   如果为 `None`，则保持现有逻辑 `{basename}.json`。

**验证:**
-   在配置文件中设置 `label_suffix: "_end"`。
-   运行主程序，检查生成的标注文件名是否为 `xxxx_end.json`，而图片名仍为 `xxxx.png`。
-   移除该配置，检查标注文件名是否恢复为 `xxxx.json`。

---

### 步骤 2: 创建概率处理工具模块

**目标:** 新建一个独立的 `prob_utils.py` 模块，封装所有与概率计算和随机选择相关的底层函数。这能确保逻辑的清晰和可复用性。

**操作:**
1.  在 `table_render/utils/` 目录下创建新文件 `prob_utils.py`。
2.  在该文件中实现以下核心函数：

    ```python
    # table_render/utils/prob_utils.py

    import random
    from typing import List, Any, Tuple, Union

    def normalize_probabilities(probabilities: List[float]) -> List[float]:
        """将概率列表归一化，使其总和为1。"""
        # ... 实现逻辑 ...
        pass

    def choose_from_list(options: List[Any], probabilities: List[float] = None, rng: random.Random = None) -> Any:
        """根据给定的概率从列表中选择一个选项。"""
        # ... 实现逻辑，包括概率归一化 ...
        pass

    def get_from_range(value_range: Union[List[int], List[float]], rng: random.Random = None) -> Union[int, float]:
        """从一个数值范围 [min, max] 中随机选择一个值（均匀分布）。"""
        # ... 实现逻辑 ...
        pass
    ```

**验证:**
- 编写单元测试来验证 `prob_utils.py` 中每个函数的正确性，覆盖各种边界情况（如概率列表为空、总和不为1等）。

### 步骤 3: 定义新的概率化数据模型

**目标:** 在 `config_models.py` 中定义新的 Pydantic 模型，用于表示概率化的配置项。

**操作:**
1.  在 `table_render/models/config_models.py` 中定义基础的概率模型。

    ```python
    # table_render/models/config_models.py
    from pydantic import BaseModel
    from typing import List, Any, Union

    class ProbabilisticRange(BaseModel):
        """定义一个概率化的数值范围列表。"""
        range_list: List[List[Union[int, float]]]
        probability_list: List[float]

    class ProbabilisticOptions(BaseModel):
        """定义一个概率化的离散选项列表。"""
        option_list: List[Any]
        probability_list: List[float]
    ```
2.  修改现有的 `StructureConfig` 模型，将 `rows` 和 `cols` 从 `int` 类型改为新的 `ProbabilisticRange` 类型或 `int` 的联合类型，以逐步迁移。

    ```python
    # table_render/models/config_models.py
    class StructureConfig(BaseModel):
        rows: Union[int, ProbabilisticRange]
        cols: Union[int, ProbabilisticRange]
        # ... 其他字段保持不变 ...
    ```

**验证:**
- 确保 Pydantic 模型可以被正确实例化和验证。可以编写一个简单的脚本，加载一个使用新格式的测试 YAML 文件，看是否能成功解析到模型中。

### 步骤 4: 重构 `Resolver` 以支持概率化结构

**目标:** 修改 `resolver.py` 中的 `_resolve_structure_params` 方法，使其能够解析新的 `ProbabilisticRange` 配置。

**操作:**
1.  在 `Resolver` 类中添加一个新的辅助方法 `_resolve_prob_range`。

    ```python
    # table_render/resolver.py
    from .utils.prob_utils import choose_from_list, get_from_range
    from .models.config_models import ProbabilisticRange

    class Resolver:
        # ...
        def _resolve_prob_range(self, prob_range: ProbabilisticRange) -> Union[int, float]:
            """解析一个概率化范围配置，返回一个确定的数值。"""
            chosen_range = choose_from_list(prob_range.range_list, prob_range.probability_list, self.rng)
            return get_from_range(chosen_range, self.rng)
    ```
2.  修改 `_resolve_structure_params` 方法，使其能够处理新的 `rows` 和 `cols` 类型。

    ```python
    # table_render/resolver.py
    def _resolve_structure_params(self, config: StructureConfig) -> StructureParams:
        # ...
        if isinstance(config.rows, ProbabilisticRange):
            rows = self._resolve_prob_range(config.rows)
        else: # 保持对旧格式（int）的兼容
            rows = config.rows
        
        # 对 cols 执行相同逻辑
        # ...
        return StructureParams(rows=rows, cols=cols, ...)
    ```

**验证:**
- 修改默认配置文件，为 `rows` 使用新的 `ProbabilisticRange` 格式。
- 运行主程序 `main.py` 生成几个样本，检查生成的表格行数是否符合预期范围和概率分布。

### 步骤 5: 实现 `border_mode` 的复杂概率化配置

**目标:** 按照我们讨论的方案，为 `border` 实现更复杂的、支持独立子配置的概率化模型。

**操作:**
1.  在 `config_models.py` 中定义 `Border` 的新模型。

    ```python
    # table_render/models/config_models.py

    class BorderModeOption(BaseModel):
        """单个边框模式的配置及其概率。"""
        probability: float
        config: dict # 使用 dict 以获得最大灵活性

    class BorderConfig(BaseModel):
        mode_options: List[BorderModeOption]
    ```
2.  修改 `StyleConfig`，使其 `border` 字段使用新的 `BorderConfig` 模型。
3.  在 `Resolver._resolve_style_params` 中，添加逻辑来解析 `BorderConfig`。

    ```python
    # table_render/resolver.py
    def _resolve_style_params(self, ...):
        # ...
        # 解析 border
        border_options = [opt.config for opt in config.style.border.mode_options]
        probabilities = [opt.probability for opt in config.style.border.mode_options]
        chosen_border_config = choose_from_list(border_options, probabilities, self.rng)
        
        # ... 将 chosen_border_config 中的具体参数（mode, color, width）解析并放入 ResolvedParams
        # ...
    ```

**验证:**
- 更新配置文件，为 `border` 添加多个 `mode_options`，每个都有不同的概率和样式（如颜色）。
- 生成多个样本，观察不同边框模式（`all`, `inside`, `none`）及其对应样式是否按预期概率出现。

### 步骤 6: 全面应用概率化配置并更新默认配置

**目标:** 将概率化逻辑推广到所有需要的配置项（如 `padding`, `font_size`, `sizing`），并创建一个完整的 `v3.4_default.yaml`。

**操作:**
1.  仿照步骤 2 和 3，在 `config_models.py` 中为 `padding`, `font_size` 等创建 `ProbabilisticRange` 或 `ProbabilisticOptions` 模型。
2.  在 `Resolver` 中对应的解析方法（主要是 `_resolve_style_params`）中添加处理新模型的逻辑。
3.  创建一个新的配置文件 `configs/v3.4_default.yaml`，全面使用新的概率化配置，以展示 v3.4 的全部功能。

**验证:**
- 使用新的 `v3.4_default.yaml` 作为输入，运行 `main.py` 生成大量样本。
- 随机抽查生成的图像，验证其结构、样式、边框等是否体现了配置文件中定义的概率多样性。确认程序运行稳定，没有错误。

### 步骤 7: 实现可变行高列宽功能 (Sizing Enhancement)

**目标:** 扩展sizing配置，支持表格中不同行和列具有不同的尺寸，模拟真实文档（如发票、报表）中的复杂布局。

**操作:**

1. **扩展配置模型** - 在 `table_render/config.py` 中：
   ```python
   class SizingConfigItem(BaseModel):
       """单个sizing配置项"""
       name: str = Field(description="配置名称，用于日志和调试")
       probability: float = Field(ge=0.0, le=1.0, description="配置启用概率")
       type: Literal["specific", "probabilistic"] = Field(description="配置类型")
       target_rows: Optional[List[int]] = Field(default=None, description="目标行索引列表")
       target_cols: Optional[List[int]] = Field(default=None, description="目标列索引列表")
       per_row_probability: Optional[float] = Field(default=None, description="每行触发概率")
       per_col_probability: Optional[float] = Field(default=None, description="每列触发概率")
       height_range: Optional[List[int]] = Field(default=None, description="行高范围[min, max]")
       width_range: Optional[List[int]] = Field(default=None, description="列宽范围[min, max]")

   class RowColumnSizeConfig(BaseModel):
       """行列尺寸配置"""
       default_row_height: Union[str, int, RangeConfig] = Field(default='auto')
       default_col_width: Union[str, int, RangeConfig] = Field(default='auto')
       row_configs: Optional[List[SizingConfigItem]] = Field(default=None)
       col_configs: Optional[List[SizingConfigItem]] = Field(default=None)
   ```

2. **创建解析后的sizing参数模型**：
   ```python
   class ResolvedSizingParams(BaseModel):
       """解析后的sizing参数"""
       row_heights: Dict[int, Union[int, str]] = Field(default_factory=dict)
       col_widths: Dict[int, Union[int, str]] = Field(default_factory=dict)
       default_row_height: Union[int, str] = Field(default='auto')
       default_col_width: Union[int, str] = Field(default='auto')
   ```

3. **重构Resolver的sizing解析逻辑** - 在 `table_render/resolver.py` 中：
   ```python
   def _resolve_sizing_params(self, sizing_config, resolved_structure, random_state):
       """解析sizing配置为具体的行列尺寸"""
       total_rows = resolved_structure.header_rows + resolved_structure.body_rows
       total_cols = resolved_structure.cols

       row_heights = {}
       col_widths = {}

       # 处理行配置 - specific类型优先于probabilistic类型
       if sizing_config.row_configs:
           occupied_rows = set()
           for config_item in sizing_config.row_configs:
               if random_state.random() < config_item.probability:
                   if config_item.type == "specific":
                       # 指定特定行
                   elif config_item.type == "probabilistic":
                       # 概率触发

       return ResolvedSizingParams(...)
   ```

4. **更新StyleBuilder支持新的sizing配置** - 在 `table_render/builders/style_builder.py` 中：
   ```python
   def _generate_sizing_rules(self, sizing: ResolvedSizingParams) -> str:
       """生成行列尺寸CSS规则"""
       rules = []

       # 生成特定行的高度规则
       for row_idx, height in sizing.row_heights.items():
           rules.append(f".row-{row_idx} {{ height: {height}px; }}")

       # 生成特定列的宽度规则
       for col_idx, width in sizing.col_widths.items():
           rules.append(f".col-{col_idx} {{ width: {width}px; }}")

       # 添加CSS盒模型设置避免padding冲突
       rules.append("td, th { box-sizing: border-box; }")

       return "\n".join(rules)
   ```

5. **更新HTML渲染器应用CSS类** - 在 `table_render/renderers/html_renderer.py` 中：
   ```python
   # 为行添加CSS类
   row_classes = [f"row-{row_model.row_index}", "default-row"]

   # 为单元格添加CSS类
   cell_classes = [f"col-{cell_model.col_index}", "default-col"]
   ```

6. **修复Pydantic V2兼容性问题**：
   - 将所有 `@validator` 更新为 `@field_validator`
   - 更新validator方法签名使用 `info` 参数
   - 添加 `@classmethod` 装饰器

7. **创建完整的配置示例** - 在 `configs/v3.4_default.yaml` 中：
   ```yaml
   sizing:
     default_row_height: "auto"
     default_col_width: "auto"

     row_configs:
       - name: "header_emphasis"
         probability: 0.4
         type: "specific"
         target_rows: [0]
         height_range: [40, 50]

       - name: "random_variations"
         probability: 0.25
         type: "probabilistic"
         per_row_probability: 0.15
         height_range: [45, 55]

     col_configs:
       - name: "wide_label_col"
         probability: 0.5
         type: "specific"
         target_cols: [0]
         width_range: [120, 150]
   ```

**验证:**
- 使用包含sizing配置的 `v3.4_default.yaml` 生成样本
- 验证生成的表格具有不同的行高和列宽
- 检查日志确认配置应用过程正确
- 确认CSS生成和HTML渲染正确应用sizing规则

### 步骤 8: 智能padding调整机制

**目标:** 实现padding与固定尺寸的冲突检测和智能调整，避免布局异常。

**操作:**

1. **在StyleBuilder中添加CSS盒模型设置**：
   ```css
   td, th { box-sizing: border-box; }
   ```

2. **实现冲突检测和日志记录**：
   - 当固定尺寸与padding冲突时记录警告日志
   - 通过CSS盒模型自动处理尺寸计算

**验证:**
- 测试极端配置（小尺寸+大padding）
- 确认布局正常且有适当的警告日志

---

---

## 3. 实现状态总结

### 3.1. 已完成的功能 ✅

**步骤1-6: 概率化配置机制**
- ✅ 标注文件后缀功能 (`label_suffix`)
- ✅ 概率处理工具模块 (`prob_utils.py`)
- ✅ 概率化数据模型 (`ProbabilisticRange`, `ProbabilisticOptions`)
- ✅ 重构Resolver支持概率化结构
- ✅ 边框模式的复杂概率化配置
- ✅ 全面应用概率化配置到字体、对齐、内边距等

**步骤7-8: 可变行高列宽功能**
- ✅ 扩展sizing配置模型 (`SizingConfigItem`, `ResolvedSizingParams`)
- ✅ 重构Resolver的sizing解析逻辑
- ✅ 更新StyleBuilder支持可变sizing的CSS生成
- ✅ 更新HTML渲染器应用新的CSS类名
- ✅ 修复Pydantic V2兼容性问题
- ✅ 创建完整的v3.4配置示例
- ✅ 智能padding调整机制（通过CSS盒模型）

### 3.2. 核心技术特性

1. **统一的概率框架**: 所有概率化配置都使用 `prob_utils.py` 中的统一函数
2. **灵活的sizing配置**: 支持指定特定行列或概率触发的可变尺寸
3. **冲突处理机制**: specific配置优先于probabilistic配置
4. **向后兼容性**: 保持对现有配置格式的完全兼容
5. **详细日志记录**: 记录配置应用过程便于调试
6. **CSS优化**: 使用box-sizing避免padding与固定尺寸的冲突

### 3.3. 配置文件示例

完整的 `configs/v3.4_default.yaml` 包含：
- 概率化表格结构配置（行数、列数、合并参数）
- 概率化样式配置（字体、对齐、内边距、颜色）
- 概率化边框模式配置
- 可变行高列宽配置（specific和probabilistic两种模式）
- 详细的配置注释和使用说明

### 3.4. 验证结果

- ✅ 所有Pydantic V2兼容性问题已修复
- ✅ 配置文件语法正确，可正常加载
- ✅ 概率化配置按预期工作
- ✅ 可变sizing功能正常生成不同行高列宽的表格
- ✅ 日志记录详细，便于调试和监控

---

**总结:**
TableRender v3.4 已成功实现了完整的概率化配置机制和可变行高列宽功能。通过8个渐进式步骤，系统现在能够从单一配置文件生成具有丰富多样性的表格样本，模拟真实文档（如发票、报表）的复杂布局。所有功能都经过验证，具备良好的向后兼容性和扩展性。

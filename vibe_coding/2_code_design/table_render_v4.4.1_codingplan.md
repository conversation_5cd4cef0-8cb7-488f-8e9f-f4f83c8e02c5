# TableRender V4.4.1 降质功能内部化编码计划

## 版本目标

将 `degradation_processor.py` 中对 `doc_degradation` 第三方模块的依赖完全内部化，实现8种降质效果的自主可控实现。采用渐进式小步迭代策略，确保每步都可验证、可运行。

## 技术方案概述

- **核心策略**：直接在 `degradation_processor.py` 中实现8个降质函数
- **实现方式**：逐个替换外部调用为内部方法，保持最小改动
- **兼容性**：保持现有调用接口不变，确保向后兼容
- **验证策略**：每步完成后立即验证，确保程序可运行

## 代码目录结构

### 受影响的现有模块
```
table_render/
├── postprocessors/
│   ├── degradation_processor.py      # 主要修改：内部化8个降质函数
│   └── image_augmentor.py            # 保持不变：调用接口不变
├── config.py                         # 保持不变：配置结构不变
└── resolver.py                       # 保持不变：参数解析不变
```

### 无新增模块
- 所有降质算法直接实现在 `degradation_processor.py` 中
- 参数配置直接硬编码到各个函数中
- 保持单文件解决方案，避免过度模块化

## 渐进式小步迭代开发步骤

### 步骤1：实现简单降质函数（第一组）
**目标**：在 `degradation_processor.py` 中直接实现3个最简单的降质函数

**修改文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 添加内部配置参数字典（硬编码参数值）
2. 实现 `_internal_gaussian_blur()` 函数
3. 实现 `_internal_gaussian_noise()` 函数
4. 实现 `_internal_global_fade()` 函数
5. 添加内部实现开关 `use_internal_implementation`

**验证方式**：
- 3个简单函数可以独立调用并产生正确结果
- 效果参数范围与原实现一致
- 程序保持稳定运行

### 步骤2：集成简单函数到处理流程
**目标**：将已实现的简单函数集成到降质处理流程，支持混合模式

**修改文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 修改 `_apply_single_effect()` 方法，支持内部实现路径
2. 为3个简单效果添加内部调用分支
3. 保持原有 doc_degradation 调用作为备选
4. 添加效果对比验证机制

**验证方式**：
- 可通过开关切换内部实现和外部实现
- 两种实现产生相似的视觉效果
- 降质处理流程正常工作

### 步骤3：实现中等复杂度降质函数（第二组）
**目标**：在 `degradation_processor.py` 中实现4个中等复杂度的降质函数

**修改文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 实现 `_internal_motion_blur()` 函数（需要自定义kernel生成）
2. 实现 `_internal_uneven_lighting()` 函数
3. 实现 `_internal_jpeg_compression()` 函数
4. 实现 `_internal_gamma_correction()` 函数
5. 集成到 `_apply_single_effect()` 方法中

**验证方式**：
- 4个中等函数可以正确工作
- 运动模糊kernel生成算法正确
- 效果质量与原实现相当

### 步骤4：实现复杂降质函数（第三组）
**目标**：实现最复杂的局部褪色和亮度对比度函数

**修改文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 实现 `_internal_local_fade()` 函数（最复杂，包含随机区域生成）
2. 实现 `_internal_brightness_contrast()` 函数
3. 添加必要的辅助函数（如随机区域生成、PIL增强等）
4. 集成到 `_apply_single_effect()` 方法中

**验证方式**：
- 局部褪色函数正确生成随机区域
- 亮度对比度调整函数符合预期
- 所有8种函数都可以正常工作

### 步骤5：完善模糊组互斥逻辑
**目标**：实现模糊效果组内的权重随机选择逻辑

**修改文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 实现 `_internal_average_blur()` 函数
2. 修改模糊效果调用逻辑，实现权重选择（高斯25%，运动50%，平均25%）
3. 优化模糊函数的性能
4. 完善模糊效果的参数处理

**验证方式**：
- 模糊组互斥逻辑正确工作
- 权重分布符合预期
- 模糊效果性能满足要求

### 步骤6：全面切换到内部实现
**目标**：完全移除对 doc_degradation 的依赖

**修改文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 移除所有 doc_degradation 相关导入语句
2. 删除外部实现的调用路径和兼容代码
3. 移除 `use_internal_implementation` 开关，默认使用内部实现
4. 清理不再需要的 `effect_mapping` 和相关逻辑
5. 更新错误处理和日志信息

**验证方式**：
- 程序不再依赖 third_parties/doc_degradation
- 所有降质效果正常工作
- 性能和效果质量保持稳定

### 步骤7：性能优化和最终验证
**目标**：优化性能，进行全面测试验证

**修改文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 优化8个内部函数的性能，减少不必要的内存分配
2. 添加详细的错误处理和边界检查
3. 完善日志记录和调试信息
4. 整理代码结构，添加函数分组注释
5. 进行全面的回归测试

**验证方式**：
- 性能测试通过，处理时间在可接受范围内
- 所有降质效果的视觉质量符合要求
- 端到端测试验证整个流程稳定

## 关键技术要点

### 单文件实现原则
- **degradation_processor.py**：所有8个降质函数直接实现在此文件中
- **参数硬编码**：将 doc_degradation 的配置参数直接硬编码到各函数中
- **函数分组**：用注释将函数按类型分组（模糊类、颜色类、压缩类等）
- **最小依赖**：只依赖 cv2、numpy、PIL 等基础库

### 兼容性保证
- 保持 `DegradationProcessor` 的公共接口不变
- 保持效果参数范围和默认值一致
- 保持错误处理行为一致

### 验证策略
- 每步完成后立即进行功能验证
- 使用相同的测试图像对比效果质量
- 监控性能指标，确保无明显退化

## 实现流程图

```mermaid
flowchart TD
    A[步骤1: 实现简单函数] --> B[步骤2: 集成简单函数]
    B --> C[步骤3: 实现中等函数]
    C --> D[步骤4: 实现复杂函数]
    D --> E[步骤5: 完善模糊逻辑]
    E --> F[步骤6: 移除外部依赖]
    F --> G[步骤7: 性能优化]

    A --> A1[高斯模糊函数]
    A --> A2[高斯噪声函数]
    A --> A3[全局褪色函数]

    C --> C1[运动模糊函数]
    C --> C2[不均匀光照函数]
    C --> C3[JPEG压缩函数]
    C --> C4[伽马校正函数]

    D --> D1[局部褪色函数]
    D --> D2[亮度对比度函数]

    E --> E1[平均模糊函数]
    E --> E2[模糊组权重逻辑]

    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style A1,A2,A3 fill:#fff3e0
    style C1,C2,C3,C4 fill:#fce4ec
    style D1,D2 fill:#f3e5f5
    style E1,E2 fill:#e8f5e8
```

## 风险控制措施

1. **渐进验证**：每步完成后立即验证，确保程序可运行
2. **备选方案**：在完全移除外部依赖前保留原实现作为备选
3. **效果对比**：使用相同测试用例对比新旧实现的效果
4. **性能监控**：持续监控处理性能，及时发现性能问题

## 预期收益

- **依赖简化**：完全移除对 third_parties/doc_degradation 的依赖
- **代码可控**：降质算法完全自主可控，便于维护和优化
- **部署简化**：减少外部依赖，简化部署流程
- **定制能力**：可根据项目需求灵活调整算法参数
- **文件精简**：单文件解决方案，避免过度模块化

---

**文档版本**：v2.0
**创建日期**：2025-01-21
**修改日期**：2025-01-21
**预计开发时间**：2-3个工作日（按步骤分配：2h + 1h + 3h + 4h + 2h + 1h + 2h）

# TableRender V2: 编码计划 - 增强的结构与内容

**目标:** 本文档为 TableRender 项目的版本2（V2）提供详细的、渐进式的编码步骤。V2 的核心是增强表格结构和内容生成的多样性，使其能够产出更复杂、更真实的合成数据。

---

## 1. 整体设计与模块影响分析

V2 的功能将主要影响配置、结构构建和内容构建三个方面。样式和渲染模块基本不受影响。

### 1.1. 目录结构

项目目录结构保持不变，与详细设计文档中定义的一致。

### 1.2. 受影响的模块

-   **`table_render/config.py`**: 需要大幅扩展，以支持所有新的可配置项，包括随机维度、单元格合并、CSV数据源和程序化内容类型。这是所有后续开发的基础。
-   **`table_render/builders/structure_builder.py`**: 将进行核心逻辑修改。需要实现随机维度选择和复杂的单元格合并算法。
-   **`table_render/builders/content_builder.py`**: 将进行核心逻辑修改。需要添加新的数据源处理能力，包括读取CSV文件和生成格式化数据。
-   **`table_render/renderers/html_renderer.py`**: 需要进行少量适配，以确保由 `StructureBuilder` 创建的合并单元格（带有 `rowspan` 和 `colspan`）能够被正确地转换为HTML属性。
-   **`requirements.txt`**: 可能需要添加新的依赖库（如 `faker`）来辅助生成格式化数据。

---

## 2. 渐进式小步迭代开发步骤

以下步骤经过精心设计，确保每一步都是一个独立、可验证的迭代。

### 步骤 1: 扩展配置模型 (`config.py`)

**目标:** 在 `config.py` 中使用 Pydantic 模型定义 V2 所需的全部新配置项。这使得新功能在逻辑实现前就已“可配置”，并为后续开发提供了清晰的数据结构。

**操作:**

1.  **修改 `StructureConfig`**:
    -   将 `rows` 和 `cols` 的类型从 `int` 修改为 `Union[int, RangeConfig]`，以支持固定值或范围值。
    -   添加新的字段用于控制单元格合并：
        -   `merge_probability: float = 0.0`
        -   `max_row_span: int = 2`
        -   `max_col_span: int = 2`

2.  **修改 `ContentConfig`**:
    -   修改 `source_type` 的 `Literal`，增加 `'csv'` 选项。
    -   添加新字段以支持不同的内容源配置：
        -   `programmatic_types: Optional[List[str]] = None`
        -   `csv_source: Optional[CSVSourceConfig] = None`

3.  **新增 `CSVSourceConfig` 模型**:
    -   创建一个新的 Pydantic 模型 `CSVSourceConfig`，包含以下字段：
        -   `file_path: str`
        -   `encoding: str = 'utf-8'`
        -   `mismatch_strategy: Literal['truncate', 'fill_empty'] = 'truncate'`

**验证:**
-   程序应能使用旧的V1配置文件正常启动。
-   创建一个包含所有V2新配置项的测试配置文件，程序启动时应能通过Pydantic的验证而不会报错。

### 步骤 2: 实现随机化表格维度

**目标:** 使 `StructureBuilder` 能够根据配置中的范围（`RangeConfig`）随机确定表格的行数和列数。

**操作:**

1.  **修改 `StructureBuilder` 的 `build` 方法**:
    -   在方法开头，检查 `config.structure.rows` 和 `config.structure.cols` 的类型。
    -   如果类型是 `RangeConfig`，则使用 `random.randint(config.min, config.max)` 来确定本次生成的具体行/列数。
    -   如果类型是 `int`，则继续使用该固定值。

**伪代码 (`structure_builder.py`):**
```python
# in StructureBuilder.build()

# 确定行数
if isinstance(self.config.rows, RangeConfig):
    num_rows = random.randint(self.config.rows.min, self.config.rows.max)
else:
    num_rows = self.config.rows

# 确定列数 (同理)
# ...
```

**验证:**
-   使用一个将行数和列数配置为范围的配置文件进行多次生成。
-   检查输出结果，确认每次生成的表格维度都在指定的范围内随机变化。

### 步骤 3: 实现单元格合并逻辑

**目标:** 在 `StructureBuilder` 中实现核心的单元格合并算法，并确保 `HtmlRenderer` 能正确渲染。

**操作:**

1.  **修改 `StructureBuilder` 的 `build` 方法**:
    -   创建一个与表格尺寸相同的二维数组 `occupation_grid`，初始值全部为 `False`，用于追踪单元格是否已被占用。
    -   在双层循环中遍历每个单元格 `(r, c)`：
        -   如果 `occupation_grid[r][c]` 为 `True`，则跳过此单元格，因为它已被合并。
        -   根据 `merge_probability` 决定是否从此单元格开始合并。
        -   如果决定合并，计算一个随机的 `row_span` 和 `col_span`（不超过 `max_row_span/col_span` 和表格边界）。
        -   在 `occupation_grid` 中，将所有被这个新合并单元格覆盖的区域标记为 `True`。
        -   创建 `CellModel`，并设置其 `row_span` 和 `col_span` 属性。
        -   如果未合并，`row_span` 和 `col_span` 保持为1。

2.  **适配 `HtmlRenderer`**:
    -   在 `render` 方法中，当将 `CellModel` 转换为 HTML 的 `<td>` 或 `<th>` 标签时，检查 `cell.row_span` 和 `cell.col_span`。
    -   如果它们大于1，则在HTML标签中添加 `rowspan="..."` 和 `colspan="..."` 属性。

**验证:**
-   使用一个启用了 `merge_probability` 的配置文件进行生成。
-   检查输出的图像，确认表格中出现了正确渲染的合并单元格。
-   （可选）检查生成的HTML，确认 `<td>`/`<th>` 标签包含了正确的 `rowspan` 和 `colspan` 属性。

### 步骤 4: 实现程序化生成格式化内容

**目标:** 使 `ContentBuilder` 能够根据配置生成如日期、货币等格式化数据。

**操作:**

1.  **（可选）添加 `faker` 依赖**:
    -   在 `requirements.txt` 中添加 `Faker` 库，它能极大地简化格式化数据的生成。

2.  **修改 `ContentBuilder` 的 `build` 方法**:
    -   检查 `config.content.source_type`。
    -   如果为 `'programmatic'`，则检查 `config.content.programmatic_types` 列表。
    -   为每种支持的类型（如 `'date'`, `'currency'`, `'percentage'`）编写一个小的生成函数。
    -   在填充单元格内容时，从 `programmatic_types` 列表中随机选择一个类型，并调用相应的生成函数来创建内容。

**伪代码 (`content_builder.py`):**
```python
# in ContentBuilder.build()

if self.config.source_type == 'programmatic':
    # ... 遍历所有单元格 ...
    content_type = random.choice(self.config.programmatic_types)
    if content_type == 'date':
        cell.content = self._generate_random_date()
    elif content_type == 'currency':
        cell.content = self._generate_random_currency()
    # ... etc
```

**验证:**
-   使用一个配置了 `programmatic_types` 的配置文件进行生成。
-   检查输出的图像，确认表格中填充了符合预期格式的数据。

### 步骤 5: 实现从 CSV 文件加载内容

**目标:** 使 `ContentBuilder` 能够从指定的 CSV 文件读取数据来填充表格。

**操作:**

1.  **修改 `ContentBuilder` 的 `build` 方法**:
    -   检查 `config.content.source_type`。
    -   如果为 `'csv'`，则从 `config.content.csv_source` 获取文件路径。
    -   使用 Python 的 `csv` 模块一次性将 CSV 文件内容读入一个二维列表 `csv_data`。
    -   遍历 `TableModel` 中的每个单元格 `(r, c)`。
    -   如果 `(r, c)` 在 `csv_data` 的范围内，则用 `csv_data[r][c]` 填充 `cell.content`。
    -   如果超出了范围（即表格比CSV大），则根据 `mismatch_strategy` 进行处理：
        -   `'truncate'` 或 `'fill_empty'`: 将单元格内容留空 (`''`)。

**验证:**
-   创建一个测试用的 `test.csv` 文件。
-   使用一个指向该CSV文件的配置文件进行生成。
-   检查输出的图像，确认表格内容与CSV文件一致。
-   测试当表格尺寸大于或小于CSV尺寸时，`mismatch_strategy` 的行为是否符合预期。

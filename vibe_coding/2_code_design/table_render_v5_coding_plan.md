# TableRender V5.0 编码计划：CSV内容源优化与随机采样

## 1. 概述

**版本目标：** 优化CSV内容源的使用方式，支持概率化目录选择和随机采样填充，提升表格内容的多样性和真实性。

**核心功能：**
1. 概率化CSV目录选择（类似字体目录机制）
2. 随机采样内容填充（替代位置对应模式）
3. 边界情况处理和向后兼容

## 2. 代码目录结构

```
table_render/
├── config.py                    # [修改] 扩展ContentConfig和CSVSourceConfig
├── resolver.py                  # [修改] 扩展内容参数解析逻辑
├── builders/
│   └── content_builder.py       # [重构] 核心修改：CSV选择和随机采样
└── utils/
    ├── csv_utils.py             # [新增] CSV文件选择和处理工具
    └── prob_utils.py            # [复用] 概率选择工具（已存在）

configs/
└── v4_css_background_test.yaml  # [修改] 更新配置示例

tests/
└── json_to_csv_converter.py     # [已存在] 数据转换脚本
```

## 3. 受影响的现有模块

### 3.1 配置模块 (config.py)
- **扩展 CSVSourceConfig**：添加csv_dirs和csv_dir_probabilities字段
- **保持向后兼容**：原有file_path字段继续支持
- **新增采样模式配置**：sampling_mode等字段

### 3.2 解析器模块 (resolver.py)
- **扩展 _resolve_content_params**：处理新的CSV目录配置
- **概率归一化**：复用现有的概率处理逻辑

### 3.3 内容构建器 (content_builder.py)
- **重构 _fill_from_csv**：从位置对应改为随机采样
- **新增 _select_csv_file**：实现概率化文件选择
- **新增 _fill_with_random_sampling**：实现随机采样逻辑

## 4. 渐进式小步迭代开发步骤

### 步骤 1：扩展配置模型
**目标：** 扩展配置模型以支持概率化CSV目录选择

**操作：**
1. 修改 `config.py` 中的 `CSVSourceConfig` 类
2. 添加新字段：`csv_dirs`, `csv_dir_probabilities`, `sampling_mode`
3. 保持 `file_path` 字段的向后兼容性
4. 更新 `ResolvedContentParams` 模型

**验证：**
- 配置文件解析不报错
- 新旧配置格式都能正常加载
- Pydantic验证通过

### 步骤 2：创建CSV工具模块
**目标：** 创建独立的CSV文件选择和处理工具

**操作：**
1. 创建 `table_render/utils/csv_utils.py`
2. 实现 `select_csv_file_from_dirs()` 函数
3. 实现 `scan_csv_files_in_dir()` 函数
4. 实现基本的错误处理和日志记录

**验证：**
- 能够扫描目录中的CSV文件
- 概率选择逻辑正确
- 错误情况处理得当

### 步骤 3：扩展配置解析器
**目标：** 更新resolver以处理新的CSV配置

**操作：**
1. 修改 `resolver.py` 中的 `_resolve_content_params` 方法
2. 添加对 `csv_dirs` 和 `csv_dir_probabilities` 的处理
3. 实现概率归一化逻辑（复用现有代码）
4. 保持向后兼容性

**验证：**
- 新配置格式正确解析
- 概率归一化正确
- 旧配置格式仍然工作

### 步骤 4：实现CSV文件选择逻辑
**目标：** 在ContentBuilder中实现概率化CSV文件选择

**操作：**
1. 修改 `content_builder.py` 中的 `_fill_from_csv` 方法
2. 新增 `_select_csv_file` 方法
3. 集成csv_utils模块的文件选择功能
4. 实现优先级逻辑：csv_dirs > file_path

**验证：**
- 能够从多个目录中选择CSV文件
- 概率分布符合配置
- 单文件模式仍然工作

### 步骤 5：实现随机采样填充逻辑
**目标：** 重构内容填充逻辑，实现随机采样

**操作：**
1. 新增 `_fill_with_random_sampling` 方法
2. 实现表头采样池管理（第一行）
3. 实现数据采样池管理（数据行）
4. 实现采样池耗尽时的重新填充机制

**验证：**
- 表头内容来自CSV第一行且不重复
- 数据内容来自CSV数据行且不重复
- 采样池耗尽后能正确重新填充

### 步骤 6：边界情况处理
**目标：** 处理各种边界情况和错误场景

**操作：**
1. 添加CSV文件验证逻辑（最少2行2列）
2. 处理目录不存在的情况
3. 处理目录无CSV文件的情况
4. 添加详细的错误日志和警告

**验证：**
- 无效CSV文件被正确跳过
- 错误情况有适当的日志输出
- 程序在异常情况下仍能运行

### 步骤 7：更新配置示例和测试
**目标：** 更新配置文件示例并进行端到端测试

**操作：**
1. 更新 `configs/v4_css_background_test.yaml`
2. 添加新的配置示例
3. 进行完整的端到端测试
4. 验证生成的表格内容多样性

**验证：**
- 配置文件正确工作
- 生成的表格内容具有随机性
- 不同运行产生不同的内容组合

## 5. 实现流程图

```mermaid
flowchart TD
    A[开始：CSV内容填充] --> B{检查配置类型}
    B -->|csv_dirs存在| C[概率选择目录]
    B -->|仅file_path| D[使用指定文件]
    
    C --> E[扫描目录CSV文件]
    E --> F[随机选择CSV文件]
    F --> G[加载CSV数据]
    D --> G
    
    G --> H{验证CSV格式}
    H -->|无效| I[跳过并尝试其他文件]
    H -->|有效| J[分离表头和数据行]
    
    J --> K[初始化采样池]
    K --> L[遍历表格单元格]
    
    L --> M{单元格类型}
    M -->|表头| N[从表头采样池选择]
    M -->|数据| O[从数据采样池选择]
    
    N --> P{表头池是否为空}
    P -->|是| Q[重新填充表头池]
    P -->|否| R[移除已选择项]
    
    O --> S{数据池是否为空}
    S -->|是| T[重新填充数据池]
    S -->|否| U[移除已选择项]
    
    Q --> R
    T --> U
    R --> V[填充单元格内容]
    U --> V
    
    V --> W{还有更多单元格?}
    W -->|是| L
    W -->|否| X[完成填充]
    
    I --> Y{还有其他文件?}
    Y -->|是| F
    Y -->|否| Z[报错：无可用CSV]
```

## 6. 关键技术要点

### 6.1 概率化选择机制
- 复用 `utils/prob_utils.py` 中的现有概率选择逻辑
- 支持概率归一化和等概率回退
- 与字体目录选择机制保持一致

### 6.2 随机采样算法
- 使用采样池（sampling pool）避免重复
- 表头池和数据池分离管理
- 池耗尽时自动重新填充

### 6.3 向后兼容性
- 保持原有 `file_path` 配置的支持
- 配置优先级：`csv_dirs` > `file_path`
- 原有的 `mismatch_strategy` 在随机模式下可忽略

### 6.4 错误处理策略
- 目录不存在：记录错误并尝试其他目录
- 无CSV文件：记录警告并尝试其他目录
- CSV格式错误：跳过文件并尝试其他文件
- 所有选项都失败：抛出明确的错误信息

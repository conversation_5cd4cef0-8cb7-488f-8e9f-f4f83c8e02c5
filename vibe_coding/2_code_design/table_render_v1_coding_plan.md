# TableRender V1 (MVP) - 编码计划

本文档详细说明了 `TableRender` 项目版本1（MVP）的开发步骤。计划遵循渐进式、小步迭代的原则，确保每一步都可独立编码、验证和集成。

**版本1目标**：搭建一个核心的端到端渲染流水线，能够根据最简化的配置，生成一个带基础边框的表格图像，并附带精确的单元格边界框（`bbox`）和逻辑位置（`lloc`）标注。

---

## 1. 最终代码目录结构 (V1)

根据详细设计文档，版本1完成后，核心代码包的结构如下：

```
TableRender/
├── table_render/           # 核心Python包
│   ├── __init__.py
│   ├── main.py             # 命令行入口
│   ├── config.py           # Pydantic 配置模型 (V1简化版)
│   ├── main_generator.py   # 核心生成器
│   ├── models.py           # 内部数据模型 (TableModel, RowModel, CellModel)
│   ├── builders/
│   │   ├── __init__.py
│   │   ├── structure_builder.py
│   │   └── content_builder.py
│   ├── renderers/
│   │   ├── __init__.py
│   │   └── html_renderer.py
│   └── utils/
│       ├── __init__.py
│       └── file_utils.py
│
├── configs/
│   └── v1_minimal.yaml     # V1的示例配置文件
│
├── output/                 # 默认输出目录 (会被.gitignore忽略)
│
└── requirements.txt        # 项目依赖
```

## 2. 受影响的模块说明

由于这是项目的初始版本，所有模块均为新建。我们将从 `synfintabgen` 和 `Table-Generator` 的分析中汲取设计思想，但不会直接复用其代码，以构建一个更现代化、更可控的架构。

- **渲染机制**：借鉴 `synfintabgen` 的“HTML渲染驱动”思想，但使用 `Playwright` 替代 `Selenium`，并采用更高效的JavaScript注入方式获取标注，以规避其性能瓶颈。
- **模块化**：借鉴 `Table-Generator` 的高度模块化设计，将结构、内容、渲染等关注点分离，但避免其复杂的外部环境依赖。

## 3. 渐进式开发与集成步骤

### 步骤 1: 项目骨架与配置系统搭建

**目标**: 创建项目的基础目录结构，并建立一个基于Pydantic的、可验证的配置加载机制。

1.  **创建目录和文件**: 按照上述“最终代码目录结构”创建所有必要的目录和空的 `__init__.py` 文件。
2.  **定义依赖**: 在 `requirements.txt` 中添加 V1 所需的核心依赖：
    ```txt
    pydantic
    pyyaml
    playwright
    ```
3.  **实现配置模型 (`config.py`)**: 创建 V1 范围内的 Pydantic 模型。此时的配置非常简化，只包含 MVP 所需的最基本字段。
    ```python
    # table_render/config.py (伪代码)
    from pydantic import BaseModel
    from typing import Literal

    class OutputConfig(BaseModel):
        output_dir: str = "./output/"

    class StructureConfig(BaseModel):
        rows: int
        cols: int

    class RenderConfig(BaseModel):
        output: OutputConfig
        structure: StructureConfig
        seed: int = 42
    ```
4.  **创建示例配置 (`v1_minimal.yaml`)**: 在 `configs/` 目录下创建一个简单的 YAML 配置文件。
    ```yaml
    # configs/v1_minimal.yaml
    output:
      output_dir: "./output/v1_test"
    structure:
      rows: 5
      cols: 4
    seed: 42
    ```

**可验证性**: 此时可以编写一个临时脚本，尝试加载 `v1_minimal.yaml` 并用 `RenderConfig` 模型进行验证。如果加载和验证成功，则此步骤完成。

### 步骤 2: 命令行入口与主生成器桩

**目标**: 创建一个可以接收命令行参数的程序入口，并搭建主生成器的基本框架。

1.  **实现命令行入口 (`main.py`)**: 使用 `argparse` 解析 `--config` 和 `--num-samples` 参数。实现加载、解析和验证配置文件（在步骤1中已定义）的逻辑。
2.  **实现主生成器桩 (`main_generator.py`)**: 创建 `MainGenerator` 类。其构造函数接收 `RenderConfig` 对象。其 `generate(num_samples)` 方法目前仅包含一个循环，打印日志信息，例如 “正在生成第 x / num_samples 个样本...”。
3.  **连接两者**: 在 `main.py` 中，成功解析配置后，实例化 `MainGenerator` 并调用其 `generate` 方法。

**可验证性**: 从命令行运行 `python -m table_render.main --config configs/v1_minimal.yaml --num-samples 3`。程序应能成功运行，无错误地解析配置，并打印三次“正在生成...”的日志信息。

### 步骤 3: 内部数据模型与结构生成器

**目标**: 定义核心的内部数据表示，并实现根据配置生成表格逻辑结构的功能。

1.  **实现数据模型 (`models.py`)**: 创建 `CellModel`, `RowModel`, `TableModel` 类，用于以渲染无关的方式表示表格。`CellModel` 必须包含一个唯一的 `cell_id`（如 `cell-r-c`）。
2.  **实现结构生成器 (`builders/structure_builder.py`)**: 创建 `StructureBuilder` 类。其 `build` 方法接收 `StructureConfig`，并返回一个 `TableModel` 实例。这个实例包含了空的 `RowModel` 和 `CellModel`，但逻辑结构（行列数、单元格ID）已经完全建立。
3.  **集成**: 在 `MainGenerator` 的 `generate` 循环中，调用 `StructureBuilder` 来创建 `TableModel`。

**可验证性**: 在 `MainGenerator` 中临时添加 `print(table_model)`。运行主程序后，应能看到代表表格结构的、被正确实例化的对象模型被打印出来。

### 步骤 4: 内容生成器

**目标**: 为已创建的表格结构填充简单的占位内容。

1.  **实现内容生成器 (`builders/content_builder.py`)**: 创建 `ContentBuilder` 类。其 `build` 方法接收一个 `TableModel` 实例。对于 V1，该方法只需遍历 `TableModel` 中的每个单元格，并为其 `content` 属性填充一个简单的字符串，如 `f"Cell ({cell.row_index}, {cell.col_index})"`。
2.  **集成**: 在 `MainGenerator` 的 `generate` 循环中，在 `StructureBuilder` 之后调用 `ContentBuilder`，将内容填充到 `TableModel` 中。

**可验证性**: `MainGenerator` 中的 `print(table_model)` 现在应输出一个不仅有结构，而且每个单元格都填充了内容的模型。

### 步骤 5: HTML渲染与图像生成

**目标**: 将内存中的 `TableModel` 转换为 HTML，并使用无头浏览器将其渲染成图像文件。

1.  **实现HTML转换**: 在 `renderers/html_renderer.py` 中创建 `HtmlRenderer` 类。添加一个私有方法 `_table_model_to_html`，它将 `TableModel` 转换为一个 HTML 字符串。此 HTML 必须：
    -   为每个 `<td>` 元素设置 `id` 属性为 `cell.cell_id`。
    -   包含一个内联 `<style>` 块，为 `td, th` 添加 `border: 1px solid black;`。
2.  **实现文件工具 (`utils/file_utils.py`)**: 创建 `FileUtils` 类，包含一个 `save_image` 方法，该方法能根据配置创建标准化的输出目录（如 `output/images/`）并保存图像数据。
3.  **实现渲染逻辑**: 在 `HtmlRenderer` 中创建 `render` 方法。该方法调用 `_table_model_to_html`，然后使用 `Playwright` 启动浏览器，设置页面内容，对表格元素截图，并返回图像的二进制数据。
4.  **集成**: 在 `MainGenerator` 中，调用 `HtmlRenderer.render`，然后使用 `FileUtils.save_image` 将返回的图像数据保存到磁盘。

**可验证性**: 运行主程序。在指定的 `output` 目录中，应能找到生成的 `.png` 表格图像。图像内容应与步骤4中生成的内容一致。

### 步骤 6: 标注生成与最终输出

**目标**: 在渲染过程中获取精确的单元格标注，并生成符合PRD规范的完整输出（图像、标注JSON、元数据JSON）。

1.  **实现标注获取**: 增强 `HtmlRenderer.render` 方法。在截图之前，使用 `page.evaluate()` 执行一段JavaScript脚本。该脚本将：
    -   查询所有ID以 `cell-` 开头的元素。
    -   获取每个元素的 `getBoundingClientRect()`。
    -   返回一个 `{[cell_id]: {x, y, width, height}}` 形式的字典。
2.  **实现标注格式化**: `HtmlRenderer.render` 方法现在应返回图像数据和原始标注字典。在 `MainGenerator` 中，将此原始标注字典转换为最终的 `annotations.json` 格式（包含 `bbox`, `lloc`, `content` 等字段）。
3.  **实现文件保存**: 扩展 `FileUtils` 以支持保存 `annotations.json` 和 `metadata.json`。
4.  **集成**: 在 `MainGenerator` 中，完成所有数据的生成和格式化后，调用 `FileUtils` 将图像、标注和元数据（即当前样本的配置）保存到各自的目录中。

**可验证性**: 运行主程序。输出目录中应包含 `images/`, `annotations/`, `metadata/` 三个子目录，且其中的文件一一对应。打开一个标注文件，其 `bbox` 值应与对应图像中单元格的像素位置精确匹配。元数据文件应包含用于生成该样本的完整配置。至此，V1 MVP 功能全部完成。

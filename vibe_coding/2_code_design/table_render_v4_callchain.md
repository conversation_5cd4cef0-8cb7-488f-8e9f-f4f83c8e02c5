# TableRender v4.0 调用链分析

## 调用链

### 节点(main)

**所在代码文件**: `table_render/main.py`

**用途**:
作为TableRender工具的命令行入口。负责设置日志，解析用户输入的参数（配置文件、样本数量和新增的`--debug`标志），调用 `load_config` 加载和验证YAML配置，然后初始化核心的 `MainGenerator`，并最终调用其 `generate` 方法来启动整个表格图像的生成流程。V4版本增强了错误处理和日志记录，并引入了调试模式。

**输入参数**:
- `--config` (str): 用户通过命令行指定的YAML配置文件路径。
- `--num-samples` (int): 用户通过命令行指定的希望生成的图像数量。
- `--debug` (bool, optional): V4新增的可选标志，用于启用调试模式，生成每个阶段的中间图像和标注。

**输出说明**:
该函数没有直接的返回值。其主要作用是在文件系统上生成图像和标注文件，或在发生错误时打印详细日志并退出程序。

**实现流程**:
```mermaid
sequenceDiagram
    participant User
    participant main as "main()"
    participant load_config as "load_config()"
    participant generator as MainGenerator

    User->>main: 执行命令行 (含 config, num_samples, --debug)
    main->>load_config: 传入 config_path
    load_config-->>main: 返回 render_config 对象
    main->>generator: 使用 render_config 和 debug_mode 实例化
    generator-->>main: 返回 generator 实例
    main->>generator: 调用 generate(num_samples)
```

### 节点(MainGenerator.generate)

**所在代码文件**: `table_render/main_generator.py`

**用途**:
作为整个表格生成流程的核心协调器，负责按顺序调用各个子模块（配置解析、结构/内容/样式构建、渲染、后处理），管理和传递数据，并处理不同模式（如CSS背景模式）下的逻辑分支。

**输入参数**:
- `config_path` (str): 主配置文件的路径。
- `output_dir` (str): 输出目录的路径。
- `num_samples` (int): 要生成的样本数量。
- `seed` (int): 全局随机种子。
- `debug` (bool): 是否启用调试模式。

**输出说明**:
- 在指定的输出目录下生成图像文件和对应的标注文件（通常是`.jpg`和`.json`）。

**实现流程**:
```mermaid
graph TD
    A["开始(generate)"] --> B["初始化随机状态"];
    B --> C{"循环生成每个样本"};
    C -->|"循环中"| D["为当前样本生成唯一ID和随机种子"];
    D --> E["调用 Resolver.resolve"];
    E --> F["调用 StructureBuilder.build"];
    F --> G["调用 ContentBuilder.build"];
    G --> H["调用 StyleBuilder.build"];
    H --> I{"使用CSS背景模式?"};
    I -->|"是"| J["调用 HtmlRenderer.render(带背景参数)"];
    J --> C;
    I -->|"否"| K["调用 HtmlRenderer.render(无背景参数)"];
    K --> L["调用 ImageAugmentor.process"];
    L --> C;
    C -->|"所有样本完成"| Z["结束"];
```

### 节点(Resolver.resolve)

**所在代码文件**: `table_render/resolver.py`

**用途**:
作为“配置翻译器”，将用户在YAML文件中定义的、包含概率和范围的泛化配置 (`RenderConfig`)，转换为一次具体生成任务所需的、完全确定性的参数集 (`ResolvedParams`)。它接收一个随机种子以确保每次解析过程可被复现。V4版本中，它的核心职责扩展到了解析新增的 `postprocessing` 配置，包括决定是否应用模糊、噪声、透视变换，并计算出具体的强度值。此外，它还负责随机选择背景图片，并计算出在CSS渲染模式下所需的精确布局参数。

**输入参数**:
- `config` (RenderConfig): 从YAML文件加载的原始配置对象。
- `seed` (int): 用于本次解析的随机种子。

**输出说明**:
- `ResolvedParams`: 一个包含所有确定性参数的数据对象，下游所有模块（Builders, Renderer, Augmentor）都将依赖此对象的属性进行工作。

**实现流程**:
```mermaid
graph TD
    A["开始(resolve)"] -->|传入 config, seed| B["创建 RandomState"];
    B --> C["调用 _resolve_structure_params"];
    B --> D["调用 _resolve_content_params"];
    B --> E["调用 _resolve_style_params"];
    subgraph V4新增
        F["调用 _resolve_postprocessing_params"];
        F --> G["解析模糊(Blur)配置"];
        F --> H["解析噪声(Noise)配置"];
        F --> I["解析透视(Perspective)配置"];
        F --> J["解析背景(Background)配置"];
        J --> K["调用 _select_background_image"];
        J --> L["调用 _calculate_css_background_params"];
    end
    B --> F;
    B --> M["调用 _resolve_output_params"];
    subgraph 汇总
        N["创建并返回 ResolvedParams 对象"]
    end
    C & D & E & F & M --> N;
    N --> O["结束"];
```

### 节点(StructureBuilder.build)

**所在代码文件**: `table_render/builders/structure_builder.py`

**用途**:
负责创建表格的逻辑“骨架”，即一个不包含任何实际内容的`TableModel`对象。它根据配置确定表格的尺寸（行、列）、创建单元格，并执行核心的单元格合并操作。此外，它还负责根据指定的模式（如全边框、无边框、半边框）为每个最终的单元格精确地分配边框样式。

**输入参数**:
- `config` (ResolvedStructureParams): 解析后的结构参数，包含行数、列数、合并概率、最大跨度等。
- `border_mode` (str): 边框模式，可选 'full', 'none', 'semi'。
- `border_details` (dict): 当模式为 'semi' 时，提供内外边框和行列分割线的显示概率。

**输出说明**:
- `TableModel`: 一个包含完整结构信息的表格模型。其中定义了所有行、所有可见单元格（已处理合并）、每个单元格的行列跨度(`row_span`, `col_span`)以及详细的边框样式(`border_style`)。所有单元格的`content`字段此时为空。

**实现流程**:
```mermaid
graph TD
    A["开始(build)"] --> B{"使用复杂表头?"};
    B -->|"是"| C["调用 _build_complex_header_table"];
    C --> G["分配边框样式"];
    B -->|"否"| D["确定行列数"];
    D --> E["创建初始网格"];
    E --> F["执行单元格合并"];
    subgraph _merge_cells_separated
        F1["在表头区域内合并"];
        F2["在主体区域内合并"];
    end
    F --> G;
    subgraph _assign_borders_to_cells
        G --> H{"边框模式?"};
        H -->|"full"| I["应用全边框"];
        H -->|"none"| J["无操作"];
        H -->|"semi"| K["应用半边框"];
        subgraph _assign_semi_borders
            K1["应用外框"];
            K2["应用表头分割线"];
            K3["应用内部随机边框"];
            subgraph _apply_internal_borders
                K3_1["为所有内部网格线生成随机可见性"];
                K3_2["将网格线可见性映射到单元格边框"];
            end
        end
    end
    I & J & K --> Z(("返回 TableModel"));
```

### 节点(ContentBuilder.build)

**所在代码文件**: `table_render/builders/content_builder.py`

**用途**:
负责为 `StructureBuilder` 创建的表格“骨架”填充实际内容。它接收一个仅有结构的`TableModel`，并根据配置，通过程序化生成或从CSV文件读取的方式，为模型中的每个单元格赋予内容。

**输入参数**:
- `table_model` (TableModel): 由`StructureBuilder`生成的、仅有结构的表格模型。
- `config` (ResolvedContentParams): 解析后的内容参数，指定了内容来源（程序化或CSV）、要生成的内容类型、CSV文件路径等。

**输出说明**:
- `TableModel`: 一个与输入模型相同，但所有单元格的`content`字段都已被填充的表格模型。

**实现流程**:
```mermaid
graph TD
    A["开始(build)"] --> B{"内容来源?"};
    B -->|"程序化(programmatic)"| C["调用 _fill_programmatically"];
    subgraph _fill_programmatically
        C1["遍历所有单元格"];
        C2{"是表头?"};
        C1 --> C2;
        C2 -->|"是"| C3["填充列标题"];
        C2 -->|"否"| C4["调用 _get_random_programmatic_content"];
        subgraph _get_random_programmatic_content
            C4_1["随机选择内容类型(日期/货币/文本等)"];
            C4_2["使用Faker库生成内容"];
        end
    end
    B -->|"CSV文件"| D["调用 _fill_from_csv"];
    subgraph _fill_from_csv
        D1["加载CSV文件数据"];
        D2["遍历所有单元格"];
        D3["根据行列索引从CSV数据中查找并填充内容"];
    end
    C & D --> Z(("返回填充内容的TableModel"));
```

### 节点(StyleBuilder.build)

**所在代码文件**: `table_render/builders/style_builder.py`

**用途**:
负责将解析后的确定性样式参数 (`ResolvedStyleParams`) 编译成一段完整的CSS代码字符串。这段CSS将直接嵌入到最终的HTML中，用于控制表格的所有视觉表现，包括字体、颜色、边框、对齐、尺寸等。在V4版本中，其核心新增功能是根据 `overflow_strategy` 配置，生成处理单元格内容溢出的CSS规则，实现文本的截断（显示省略号）或自动换行。

**输入参数**:
- `config` (ResolvedStyleParams): 由 `Resolver` 生成的确定性样式参数。
- `table_model` (TableModel): 表格结构模型，主要用于生成更精确的边框和合并单元格对齐样式。

**输出说明**:
- `str`: 一个包含所有CSS规则的字符串。

**实现流程**:
```mermaid
graph TD
    A["开始(build)"] -->|"传入 config, table_model"| B["生成@font-face规则"];
    B --> C["生成全局/基础样式"];
    C --> D["生成表头(thead)和主体(tbody)样式"];
    D --> E["生成分层/覆盖样式(行/列/单元格)"];
    E --> F["生成尺寸(sizing)样式"];
    F --> G["生成边框(border)样式"];
    G --> H["生成合并单元格对齐优化样式"];
    subgraph V4新增
        I["调用 _generate_overflow_rules"];
        I -->|"overflow_strategy is 'truncate'"| J["生成截断CSS"];
        I -->|"overflow_strategy is 'wrap'"| K["生成换行CSS"];
    end
    H --> I;
    J & K & I --> L["汇总所有CSS片段"];
    L --> M["返回完整的CSS字符串"];
    M --> N["结束"];
```

### 节点(HtmlRenderer.render)

**所在代码文件**: `table_render/renderers/html_renderer.py`

**用途**:
作为渲染引擎，负责将内存中的表格模型 (`TableModel`) 和样式 (`css_string`) 转换为最终的图像文件。它利用 Playwright 无头浏览器加载动态生成的HTML页面并进行截图。在V4版本中，它支持两种核心渲染模式：
1.  **传统模式**: 仅渲染表格本身，生成一张干净的表格图。
2.  **CSS背景模式**: 将表格与背景图、透视变换等效果在HTML/CSS层面直接合成，然后对整个浏览器视口进行截图。此模式下，它还必须对从浏览器获取的原始标注坐标进行修正，以匹配包含背景的更大画布。

**输入参数**:
- `table_model` (TableModel): 包含表格结构和内容的对象。
- `css_string` (str): 由 `StyleBuilder` 生成的CSS样式字符串。
- `background_params` (ResolvedPostprocessingParams): V4新增参数，包含背景图路径、透视变换矩阵、表格在背景上的位置等信息。此参数是否存在决定了采用何种渲染模式。

**输出说明**:
- `Tuple[bytes, Dict[str, Any]]`: 一个元组，包含渲染后的图像字节流和其对应的精确标注数据（单元格坐标、内容等）。

**实现流程**:
```mermaid
graph TD
    A["开始(render)"] -->|"传入table_model, css_string, background_params"| B["调用 _table_model_to_html"];
    subgraph _table_model_to_html
        B1["调用 _generate_css_with_background"];
        B1 -->|"background_params存在"| B2["调用 _generate_background_css"];
        B1 -->|"background_params存在"| B3["调用 _generate_table_transform_css"];
        B2 & B3 --> B4["组合成完整的style块"];
        B1 -->|"background_params不存在"| B4;
        B4 --> B5["根据TableModel生成HTML结构"];
        B5 --> B6["返回完整的HTML字符串"];
    end
    B --> C["Playwright: 创建新页面(page)"];
    C --> D["设置视口尺寸"];
    D --> E["page.set_content(html)"];
    E --> F["page.evaluate(JS脚本获取原始标注)"];
    F -->|"CSS模式"| G["截取整个页面(page.screenshot)"];
    G --> H["调用 _adjust_annotations_for_css_mode"];
    subgraph _adjust_annotations_for_css_mode
        H1["获取表格实际偏移量(table_bbox)"];
        H2["将所有单元格坐标加上偏移量"];
        H2 --> H3["返回修正后的标注"];
    end
    H --> I["返回(图像, 修正后的标注)"];
    F -->|"传统模式"| J["截取表格元素(table_element.screenshot)"];
    J --> K["返回(图像, 原始标注)"];
    I & K --> Z(("结束"));
```

### 节点(ImageAugmentor.process)

**所在代码文件**: `table_render/postprocessors/image_augmentor.py`

**用途**:
作为图像后处理的核心引擎，负责对 `HtmlRenderer` 生成的原始表格图像应用一系列视觉增强效果。这是V4版本“传统模式”下实现复杂视觉效果的关键。它按顺序执行模糊、噪声、透视变换，并最终可以将处理后的表格合成到一张背景图上。所有变换都会精确地同步更新到标注数据上。

**输入参数**:
- `image_bytes` (bytes): 原始表格图像的字节流。
- `annotations` (Dict): 原始图像对应的标注数据。
- `params` (ResolvedPostprocessingParams): 包含所有后处理效果开关和具体参数的对象，如模糊半径、噪声强度、透视变换偏移比例、背景图路径等。

**输出说明**:
- `Tuple[bytes, Dict[str, Any]]`: 一个元组，包含经过所有后处理效果增强后的最终图像字节流，以及与之完全匹配的、经过多次坐标变换后的最终标注数据。

**实现流程**:
```mermaid
graph TD
    A["开始(process)"] -->|"传入图像, 标注, 参数"| B["调用 augment"];
    subgraph augment
        B1["应用模糊(如果启用)"];
        B1 --> B2["应用噪声(如果启用)"];
        B2 --> B3["应用透视变换(如果启用)"];
        subgraph _apply_perspective_transform_with_annotations
            B3_1["计算随机透视变换矩阵"];
            B3_2["使用OpenCV(warpPerspective)扭曲图像"];
            B3_3["使用变换矩阵更新所有标注坐标"];
        end
        B3 --> B4["应用背景合成(如果启用)"];
        subgraph _apply_background_composition
            B4_1["委托: 调用 BackgroundComposer.compose"];
        end
        B4 --> B5["返回(处理后图像, 处理后标注)"];
    end
    B --> C["结束"];

    subgraph BackgroundComposer.compose
        D["开始合成"];
        D --> E["加载背景图"];
        E --> F{"决策: 缩放表格还是缩放背景?"};
        F -->|"缩放表格"| G["缩放表格图像和标注"];
        F -->|"缩放背景"| H["缩放背景图"];
        G & H --> I["在背景上随机选择粘贴位置"];
        I --> J["将表格粘贴到背景上"];
        J --> K["更新标注坐标(加上粘贴偏移)"];
        K --> L{"决策: 智能裁剪还是随机裁剪?"};
        L -->|"智能裁剪"| M["根据边距配置计算裁剪框"];
        L -->|"随机裁剪"| N["计算包含表格的随机裁剪框"];
        M & N --> O["裁剪合成后的图像"];
        O --> P["更新标注坐标(减去裁剪偏移)"];
        P --> Q["返回(最终图像, 最终标注)"];
    end
```

### 节点(MainGenerator.generate)

**所在代码文件**: `table_render/main_generator.py`

**用途**:
作为核心协调器，异步地执行完整的表格图像生成流水线。V4版本对此模块进行了重大重构，以支持新的图像后处理功能。它现在能够处理两种核心工作流：传统的“渲染后处理”模式，以及新增的“CSS背景渲染”模式。在每次循环中，它会调用`Resolver`获取确定性参数，然后依次调用各个`Builder`构建表格模型和样式，接着调用`HtmlRenderer`进行渲染，最后，根据配置和渲染模式，选择性地调用`ImageAugmentor`对图像和标注进行增强处理（如模糊、噪声、透视变换、背景合成和智能边距裁剪）。

**输入参数**:
- `num_samples` (int): 需要生成的表格图像样本数量。

**输出说明**:
该方法没有直接的返回值。它将最终生成的图像、结构化标注和包含可复现参数的元数据文件保存到由配置指定的输出目录中。

**实现流程**:
```mermaid
graph TD
    A["开始(generate)"] --> B["调用 asyncio.run(_async_generate)"];
    subgraph _async_generate
        C["初始化 HtmlRenderer, AnnotationConverter"];
        C --> D{"循环N次(num_samples)"};
        D -->|"进行中"| E["生成样本随机种子"];
        E --> F["调用 Resolver.resolve 获取确定性参数"];
        F --> G["调用 Builders(Structure, Content, Style)"];
        G --> H{"是否为CSS背景模式?"};
        H -->|"是"| I["调用 HtmlRenderer.render(含背景CSS)"];
        I --> J["[V4] 调用 ImageAugmentor 应用模糊/噪声"];
        J --> K["[V4] 调用 _apply_smart_crop 智能裁剪"];
        H -->|"否(传统模式)"| L["调用 HtmlRenderer.render(无背景)"];
        L --> M["[V4] 调用 ImageAugmentor 应用所有后处理效果"];
        K --> N["转换标注并保存文件"];
        M --> N;
        N --> D;
    end
    B --> C;
    D -->|"循环完成"| O["关闭 HtmlRenderer(finally)"];
    O --> P["结束"];
```
